import React from "react";
import { Container, Heading, Badge, Text } from "@camped-ai/ui";
import { TwoColumnPage } from "../../../../../components/layout/pages/two-column-page/two-column-page";
import { SectionRow } from "../../../../../components/common/section/section-row";
import { MetadataSection } from "../../../../../components/common/metadata-section";
import { ConciergeOrderItem } from "../../../../hooks/supplier-management/use-on-request-items";
import { useAdminCurrencies } from "../../../../hooks/use-admin-currencies";
import { formatCurrencyAmount, fromSmallestUnit } from "../../../../utils/currency-utils";

// Status badge colors for ConciergeOrderItem
const statusColors = {
  under_review: "orange",
  client_confirmed: "green",
  order_placed: "blue",
  cancelled: "red",
  completed: "purple",
} as const;



interface OnRequestItemDetailsLayoutProps {
  item: ConciergeOrderItem;
}

const OnRequestItemDetailsLayout: React.FC<OnRequestItemDetailsLayoutProps> = ({
  item,
}) => {
  // Get default currency from store settings
  const { defaultCurrency, isLoading: isCurrencyLoading } = useAdminCurrencies();

  // Helper functions
  const getStatusBadgeVariant = (status: string) => {
    return statusColors[status as keyof typeof statusColors] || "grey";
  };

  // Format currency using the default currency from store settings
  const formatCurrency = (amount: number, currencyCode?: string) => {
    const currency = defaultCurrency || { currency_code: "USD", symbol: "$", decimal_digits: 2 };
    const code = currencyCode || currency.currency_code;

    // Convert from smallest unit (cents) to display unit
    const displayAmount = fromSmallestUnit(amount, currency);

    return formatCurrencyAmount(displayAmount, currency, {
      showSymbol: true,
      showCode: false,
      symbolPosition: "before",
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatDateOnly = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Item Information Section
  const ItemInformationSection = () => (
    <Container className="divide-y p-0">
      <div className="flex items-center justify-between px-6 py-4">
        <Heading level="h2">Item Information</Heading>
      </div>

      <SectionRow
        title="Item ID"
        value={item.id}
      />
      <SectionRow
        title="Title"
        value={item.title || "N/A"}
      />
      <SectionRow
        title="Status"
        value={
          <Badge color={getStatusBadgeVariant(item.status)} size="small">
            {item.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
          </Badge>
        }
      />
      <SectionRow
        title="Quantity"
        value={item.quantity?.toString() || "0"}
      />
      <SectionRow
        title="Unit Price"
        value={formatCurrency(item.unit_price || 0)}
      />
      <SectionRow
        title="Total Price"
        value={formatCurrency((item.unit_price || 0) * (item.quantity || 0))}
      />
      <SectionRow
        title="Is Active"
        value={item.is_active ? "Yes" : "No"}
      />
      <SectionRow
        title="Created"
        value={item.created_at ? formatDate(item.created_at) : "N/A"}
      />
      <SectionRow
        title="Last Updated"
        value={item.updated_at ? formatDate(item.updated_at) : "N/A"}
      />
    </Container>
  );

  // Concierge Order Information Section
  const ConciergeOrderInformationSection = () => {
    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Concierge Order Information</Heading>
        </div>

        <SectionRow
          title="Concierge Order ID"
          value={item.concierge_order_id}
        />
        {item.line_item_id && (
          <SectionRow
            title="Line Item ID"
            value={item.line_item_id}
          />
        )}
        {item.variant_id && (
          <SectionRow
            title="Variant ID"
            value={item.variant_id}
          />
        )}
        {item.added_by && (
          <SectionRow
            title="Added By"
            value={item.added_by}
          />
        )}
        {item.finalized_by && (
          <SectionRow
            title="Finalized By"
            value={item.finalized_by}
          />
        )}
      </Container>
    );
  };

  // Relationship IDs Section for debugging and tracking
  const RelationshipIDsSection = () => {
    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Relationship IDs</Heading>
          <Text className="text-ui-fg-muted">For debugging and tracking</Text>
        </div>

        <SectionRow
          title="Concierge Order Item ID"
          value={
            <div className="flex items-center gap-2">
              <Text className="font-mono text-sm">{item.id}</Text>
              <button
                onClick={() => {
                  navigator.clipboard.writeText(item.id);
                }}
                className="text-ui-fg-muted hover:text-ui-fg-subtle text-xs"
                title="Copy to clipboard"
              >
                📋
              </button>
            </div>
          }
        />

        <SectionRow
          title="Concierge Order ID"
          value={
            <div className="flex items-center gap-2">
              <Text className="font-mono text-sm">{item.concierge_order_id}</Text>
              <button
                onClick={() => {
                  navigator.clipboard.writeText(item.concierge_order_id);
                }}
                className="text-ui-fg-muted hover:text-ui-fg-subtle text-xs"
                title="Copy to clipboard"
              >
                📋
              </button>
            </div>
          }
        />

        {item.concierge_order?.id && (
          <SectionRow
            title="Concierge Order (Enhanced) ID"
            value={
              <div className="flex items-center gap-2">
                <Text className="font-mono text-sm">{item.concierge_order.id}</Text>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(item.concierge_order.id);
                  }}
                  className="text-ui-fg-muted hover:text-ui-fg-subtle text-xs"
                  title="Copy to clipboard"
                >
                  📋
                </button>
              </div>
            }
          />
        )}

        {item.order?.id && (
          <SectionRow
            title="Root Order ID"
            value={
              <div className="flex items-center gap-2">
                <Text className="font-mono text-sm">{item.order.id}</Text>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(item.order.id);
                  }}
                  className="text-ui-fg-muted hover:text-ui-fg-subtle text-xs"
                  title="Copy to clipboard"
                >
                  📋
                </button>
              </div>
            }
          />
        )}

        {item.order_line_item?.id && (
          <SectionRow
            title="Order Line Item ID"
            value={
              <div className="flex items-center gap-2">
                <Text className="font-mono text-sm">{item.order_line_item.id}</Text>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(item.order_line_item.id);
                  }}
                  className="text-ui-fg-muted hover:text-ui-fg-subtle text-xs"
                  title="Copy to clipboard"
                >
                  📋
                </button>
              </div>
            }
          />
        )}

        {item.order_item?.id && (
          <SectionRow
            title="Order Item ID"
            value={
              <div className="flex items-center gap-2">
                <Text className="font-mono text-sm">{item.order_item.id}</Text>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(item.order_item.id);
                  }}
                  className="text-ui-fg-muted hover:text-ui-fg-subtle text-xs"
                  title="Copy to clipboard"
                >
                  📋
                </button>
              </div>
            }
          />
        )}

        {item.line_item_id && (
          <SectionRow
            title="Line Item ID (Reference)"
            value={
              <div className="flex items-center gap-2">
                <Text className="font-mono text-sm">{item.line_item_id}</Text>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(item.line_item_id);
                  }}
                  className="text-ui-fg-muted hover:text-ui-fg-subtle text-xs"
                  title="Copy to clipboard"
                >
                  📋
                </button>
              </div>
            }
          />
        )}

        {item.variant_id && (
          <SectionRow
            title="Variant ID"
            value={
              <div className="flex items-center gap-2">
                <Text className="font-mono text-sm">{item.variant_id}</Text>
                <button
                  onClick={() => {
                    navigator.clipboard.writeText(item.variant_id);
                  }}
                  className="text-ui-fg-muted hover:text-ui-fg-subtle text-xs"
                  title="Copy to clipboard"
                >
                  📋
                </button>
              </div>
            }
          />
        )}
      </Container>
    );
  };

  // Financial Information Section
  const FinancialInformationSection = () => {
    // Use order_line_item data when available, fall back to item data
    const unitPrice = item.order_line_item?.unit_price ?? item.unit_price;
    const quantity = item.order_item?.quantity ?? item.quantity;
    const currency = item.order?.currency_code ?? defaultCurrency?.currency_code ?? "USD";

    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Selling Price Information</Heading>
          {item.order_line_item && (
            <Badge color="blue" size="small">From Order Line Item</Badge>
          )}
        </div>

        <SectionRow
          title="Unit Price"
          value={formatCurrency(unitPrice || 0, currency)}
        />
        <SectionRow
          title="Quantity"
          value={quantity?.toString() || "0"}
        />
        <SectionRow
          title="Total Selling Price"
          value={
            <Text className="font-semibold text-lg">
              {formatCurrency((unitPrice || 0) * (quantity || 0), currency)}
            </Text>
          }
        />
        <SectionRow
          title="Currency"
          value={currency}
        />

        {item.order_line_item && item.unit_price !== item.order_line_item.unit_price && (
          <>
            <SectionRow
              title="Original Item Price"
              value={formatCurrency(item.unit_price || 0, currency)}
            />
            <SectionRow
              title="Price Difference"
              value={
                <Text className={`font-medium ${(item.order_line_item.unit_price - item.unit_price) >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {formatCurrency(Math.abs(item.order_line_item.unit_price - item.unit_price), currency)}
                  {(item.order_line_item.unit_price - item.unit_price) >= 0 ? ' higher' : ' lower'}
                </Text>
              }
            />
          </>
        )}
      </Container>
    );
  };

  // Timeline Information Section
  const TimelineInformationSection = () => {
    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Timeline Information</Heading>
        </div>

        <SectionRow
          title="Added At"
          value={item.added_at ? formatDate(item.added_at) : "N/A"}
        />
        {item.finalized_at && (
          <SectionRow
            title="Finalized At"
            value={formatDate(item.finalized_at)}
          />
        )}
        <SectionRow
          title="Created At"
          value={item.created_at ? formatDate(item.created_at) : "N/A"}
        />
        <SectionRow
          title="Updated At"
          value={item.updated_at ? formatDate(item.updated_at) : "N/A"}
        />
        {item.deleted_at && (
          <SectionRow
            title="Deleted At"
            value={formatDate(item.deleted_at)}
          />
        )}
      </Container>
    );
  };

  // Status Information Section
  const StatusInformationSection = () => {
    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Status Information</Heading>
        </div>

        <SectionRow
          title="Current Status"
          value={
            <Badge color={getStatusBadgeVariant(item.status)} size="small">
              {item.status.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
            </Badge>
          }
        />
        <SectionRow
          title="Is Active"
          value={
            <Badge color={item.is_active ? "green" : "red"} size="small">
              {item.is_active ? "Active" : "Inactive"}
            </Badge>
          }
        />
        {item.added_by && (
          <SectionRow
            title="Added By"
            value={item.added_by}
          />
        )}
        {item.finalized_by && (
          <SectionRow
            title="Finalized By"
            value={item.finalized_by}
          />
        )}
      </Container>
    );
  };

  // Metadata Section
  const MetadataFieldsSection = () => {
    const metadata = item.metadata || {};
    const hasMetadata = Object.keys(metadata).length > 0;

    return (
      <Container className="divide-y p-0">
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h2">Metadata</Heading>
          <Text className="text-ui-fg-muted">
            {Object.keys(metadata).length} field{Object.keys(metadata).length !== 1 ? "s" : ""}
          </Text>
        </div>

        {hasMetadata ? (
          Object.entries(metadata).map(([key, value]) => (
            <SectionRow
              key={key}
              title={key}
              value={typeof value === 'object' ? JSON.stringify(value) : String(value)}
            />
          ))
        ) : (
          <div className="px-6 py-4">
            <Text className="text-ui-fg-muted">No metadata available</Text>
          </div>
        )}
      </Container>
    );
  };

  return (
    <TwoColumnPage
      widgets={{
        before: [],
        after: [],
        sideBefore: [],
        sideAfter: [],
      }}
      data={item}
      showJSON={false}
      showMetadata={!!item.metadata}
      hasOutlet={false}
    >
      <TwoColumnPage.Main>
        <ItemInformationSection />
        <ConciergeOrderInformationSection />
        <RelationshipIDsSection />
        <MetadataFieldsSection />
        <TimelineInformationSection />
      </TwoColumnPage.Main>

      <TwoColumnPage.Sidebar>
        <FinancialInformationSection />
        <StatusInformationSection />
        {item.metadata && <MetadataSection data={item} />}
      </TwoColumnPage.Sidebar>
    </TwoColumnPage>
  );
};

export default OnRequestItemDetailsLayout;
