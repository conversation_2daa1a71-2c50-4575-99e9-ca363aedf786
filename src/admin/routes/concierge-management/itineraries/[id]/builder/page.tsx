import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Text } from "@camped-ai/ui";
import { useParams } from "react-router-dom";
import { lazy, Suspense } from "react";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";

import { RoleGuard } from "../../../../../components/rbac/RoleGuard";

// Dynamically import page client for better performance
const ItineraryBuilder = lazy(() => import("./page-client"));

const ItineraryBuilderPage = () => {
  const { id } = useParams();

  return (
    <>
      <PermissionBasedSidebarHider />
      <Container className="divide-y p-0 border-none shadow-none bg-transparent">
        <RoleGuard
          requirePermission="concierge_management:edit"
          fallback={
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to edit itineraries.
              </Text>
            </div>
          }
        >
          <Suspense
            fallback={
              <Container className="p-6">
                <div className="flex items-center justify-center py-12">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <span className="ml-3 text-sm text-gray-600">Loading itinerary builder...</span>
                </div>
              </Container>
            }
          >
            <ItineraryBuilder itineraryId={id} />
          </Suspense>
        </RoleGuard>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Itinerary Builder",
});

export default ItineraryBuilderPage;
