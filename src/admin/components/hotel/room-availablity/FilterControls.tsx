import React, { useState, useEffect } from "react";
import { DatePicker, Button } from "@camped-ai/ui";
import { RefreshCw } from "lucide-react";
import { Room } from "../types";
import { RoomInventoryStatus } from "../types/booking";
import RoomTypeFilter from "./RoomTypeFilter";
import RoomsFilter from "./RoomsFilter";
import ClearFiltersButton from "./ClearFiltersButton";

interface RoomTypeConfig {
  id: string;
  name: string;
}

interface FilterControlsProps {
  selectedRoomTypes: string[];
  onRoomTypesChange: (types: string[]) => void;
  selectedRooms: string[];
  onRoomsChange: (rooms: string[]) => void;
  selectedStatuses: RoomInventoryStatus[];
  onStatusesChange: (statuses: RoomInventoryStatus[]) => void;
  startDate: Date;
  endDate: Date;
  roomTypes: string[];
  roomTypeConfigs?: RoomTypeConfig[]; // New prop for ID-to-name mapping
  rooms: Room[];
  onApplyDateRange?: (startDate: Date, endDate: Date) => void;
  isLoading?: boolean;
}

const FilterControls: React.FC<FilterControlsProps> = ({
  selectedRoomTypes,
  onRoomTypesChange,
  selectedRooms,
  onRoomsChange,
  selectedStatuses,
  onStatusesChange,
  startDate,
  endDate,
  roomTypes,
  roomTypeConfigs,
  rooms,
  onApplyDateRange,
  isLoading = false,
}) => {
  // Local state for pending date changes
  const [pendingStartDate, setPendingStartDate] = useState(startDate);
  const [pendingEndDate, setPendingEndDate] = useState(endDate);

  // Track if dates have changed and need to be applied
  const [hasDateChanges, setHasDateChanges] = useState(false);

  // Update local state when props change (from external sources)
  useEffect(() => {
    setPendingStartDate(startDate);
    setPendingEndDate(endDate);
    setHasDateChanges(false);
  }, [startDate, endDate]);

  // Smart date selection handlers with validation
  const handleStartDateChange = (date: Date | null) => {
    if (!date) return;

    // Smart validation - if start > end, auto-adjust end date
    if (date > pendingEndDate) {
      const newEndDate = new Date(date);
      newEndDate.setDate(newEndDate.getDate() + 6); // Default 7-day span
      setPendingEndDate(newEndDate);
    }
    setPendingStartDate(date);
    setHasDateChanges(true);
  };

  const handleEndDateChange = (date: Date | null) => {
    if (!date) return;

    // Smart validation - if end < start, auto-adjust start date
    if (date < pendingStartDate) {
      const newStartDate = new Date(date);
      newStartDate.setDate(newStartDate.getDate() - 6); // Default 7-day span
      setPendingStartDate(newStartDate);
    }
    setPendingEndDate(date);
    setHasDateChanges(true);
  };

  // Apply the date range changes
  const handleApplyDateRange = () => {
    // Only call onApplyDateRange which handles both date updates and data refresh
    // This prevents duplicate API calls that were happening when we called
    // onStartDateChange and onEndDateChange separately
    onApplyDateRange?.(pendingStartDate, pendingEndDate);
    setHasDateChanges(false);
  };

  const clearAllFilters = () => {
    onRoomTypesChange([]);
    onStatusesChange([]);
    // Reset to default date range
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const defaultEnd = new Date(today);
    defaultEnd.setDate(defaultEnd.getDate() + 13);
    // Use onApplyDateRange to avoid duplicate API calls
    onApplyDateRange?.(today, defaultEnd);
    // Update local pending state to reflect the reset
    setPendingStartDate(today);
    setPendingEndDate(defaultEnd);
    setHasDateChanges(false);
  };

  const hasActiveFilters =
    selectedRoomTypes.length > 0 || selectedStatuses.length > 0;

  return (
    <div className="flex flex-wrap  items-center gap-4">
      {/* From Date */}
      <div className="flex flex-col">
        <label
          htmlFor="start-date-picker"
          className="text-xs text-muted-foreground mb-1"
        >
          From
        </label>
        <DatePicker
          id="start-date-picker"
          value={pendingStartDate}
          onChange={handleStartDateChange}
          className="text-sm"
          aria-label="Select start date for room availability timeline"
        />
      </div>

      {/* To Date */}
      <div className="flex flex-col">
        <label
          htmlFor="end-date-picker"
          className="text-xs text-muted-foreground mb-1"
        >
          To
        </label>
        <DatePicker
          id="end-date-picker"
          value={pendingEndDate}
          onChange={handleEndDateChange}
          className="text-sm"
          aria-label="Select end date for room availability timeline"
        />
      </div>

      {/* Apply Button */}
      {hasDateChanges && (
        <div className="flex flex-col">
          <label className="text-xs text-transparent mb-1">Apply</label>
          <Button
            onClick={handleApplyDateRange}
            disabled={isLoading}
            size="small"
            className="text-sm"
          >
            {isLoading ? (
              <>
                <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
                Loading...
              </>
            ) : (
              <>
                <RefreshCw className="w-4 h-4 mr-2" />
                Apply
              </>
            )}
          </Button>
        </div>
      )}

      <RoomTypeFilter
        selectedRoomTypes={selectedRoomTypes}
        onRoomTypesChange={onRoomTypesChange}
        roomTypes={roomTypes}
        roomTypeConfigs={roomTypeConfigs}
      />

      <RoomsFilter
        selectedRooms={selectedRooms}
        onRoomsChange={onRoomsChange}
        rooms={rooms}
        selectedRoomTypes={selectedRoomTypes}
        roomTypeConfigs={roomTypeConfigs}
      />

   

      {hasActiveFilters && <ClearFiltersButton onClear={clearAllFilters} />}
    </div>
  );
};

export default FilterControls;
