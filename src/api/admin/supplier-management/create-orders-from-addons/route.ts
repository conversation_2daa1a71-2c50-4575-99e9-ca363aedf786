import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { CreateSupplierOrderWorkflow } from "../../../../workflows/vendor_management/supplier/create-supplier-order";

interface BookingAddon {
  id: string;
  order_id: string;
  add_on_id: string;
  add_on_name: string;
  quantity: number;
  unit_price: number;
  total_price: number;
  customer_field_responses: Record<string, any>;
  order?: {
    id: string;
    display_id: string;
    email: string;
    metadata?: {
      customer_name?: string;
      check_in_date?: string;
      check_out_date?: string;
      hotel_name?: string;
    };
  };
  add_on?: {
    id: string;
    name: string;
    metadata?: {
      supplier_id?: string;
      supplier_name?: string;
    };
  };
}

interface CreateSupplierOrdersInput {
  supplier_groups: {
    supplier_id: string;
    supplier_name: string;
    addons: BookingAddon[];
    order_name?: string;
    append_to_existing_order?: string;
    delivery_date?: string;
    notes?: string;
  }[];
}

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("🔍 Creating supplier orders from booking add-ons...");

    const { supplier_groups }: CreateSupplierOrdersInput = req.body;

    if (!supplier_groups || !Array.isArray(supplier_groups)) {
      return res.status(400).json({
        type: "invalid_data",
        message: "supplier_groups is required and must be an array",
      });
    }

    const createdOrders = [];
    const errors = [];

    // Create supplier order for each supplier group
    for (const group of supplier_groups) {
      try {
        console.log(
          `🔍 Creating order for supplier: ${group.supplier_name} (${group.addons.length} add-ons)`
        );

        // Debug: Log addon structure to understand data format
        console.log("🔍 First addon structure:", JSON.stringify(group.addons[0], null, 2));

        // Transform booking add-ons to supplier order items
        const orderItems = group.addons.map((addon) => {
          const item_id = addon.add_on_id || addon.add_on?.id || addon.id;
          console.log(`🔍 Resolving item_id for ${addon.add_on_name}:`, {
            add_on_id: addon.add_on_id,
            nested_id: addon.add_on?.id,
            addon_id: addon.id,
            resolved_item_id: item_id
          });

          if (!item_id) {
            throw new Error(`Missing item_id for addon: ${addon.add_on_name}. Available fields: ${Object.keys(addon).join(', ')}`);
          }

          return {
            item_type: "service" as const,
            item_id: item_id,
            item_name: addon.add_on_name,
            item_description: `Booking add-on service for order ${
              addon.order?.display_id || addon.order_id
            }`,
            quantity: addon.quantity,
            unit_price: addon.unit_price,
            service_date: addon.order?.metadata?.check_in_date
              ? new Date(addon.order.metadata.check_in_date)
              : undefined,
            specifications: addon.customer_field_responses,
            notes: `Customer: ${
              addon.order?.metadata?.customer_name ||
              addon.order?.email ||
              "Unknown"
            }`,
          };
        });

        // Get customer info from first add-on (assuming all add-ons in group are for same customer)
        const firstAddon = group.addons[0];
        const customerInfo = {
          customer_name:
            firstAddon.order?.metadata?.customer_name ||
            firstAddon.order?.email ||
            "Unknown Customer",
          customer_email: firstAddon.order?.email || "",
          hotel_id: firstAddon.order?.metadata?.hotel_name || "",
          booking_id: firstAddon.order_id,
        };

        // Determine delivery date (use check-in date if available)
        const requestedDeliveryDate = firstAddon.order?.metadata?.check_in_date
          ? new Date(firstAddon.order.metadata.check_in_date)
          : undefined;

        // Create supplier order using the workflow
        const { result } = await CreateSupplierOrderWorkflow(req.scope).run({
          input: {
            supplier_id: group.supplier_id,
            order_type: "service" as const,
            requested_delivery_date: group.delivery_date
              ? new Date(group.delivery_date)
              : requestedDeliveryDate,
            notes:
              group.notes ||
              `Order created from ${group.addons.length} booking add-on(s)`,
            internal_notes: `Auto-generated from booking add-ons: ${group.addons
              .map((a) => a.id)
              .join(", ")}`,
            order_name: group.order_name, // Custom order name
            append_to_existing_order: group.append_to_existing_order, // Append to existing order
            ...customerInfo,
            items: orderItems,
            booking_addon_ids: group.addons.map((a) => a.id), // Pass booking add-on IDs for linking
            metadata: {
              source: "booking_addons",
              booking_addon_ids: group.addons.map((a) => a.id),
              created_from_addons_at: new Date().toISOString(),
            },
          },
        });

        createdOrders.push({
          supplier_id: group.supplier_id,
          supplier_name: group.supplier_name,
          order_id: result.order.id,
          order_number: result.order.order_number,
          total_amount: result.order.total_amount,
          items_count: result.items.length,
        });

        console.log(
          `✅ Created supplier order ${result.order.order_number} for ${group.supplier_name}`
        );
      } catch (orderError) {
        console.error(
          `❌ Failed to create order for supplier ${group.supplier_name}:`,
          orderError
        );
        errors.push(
          `Failed to create order for ${group.supplier_name}: ${orderError.message}`
        );
      }
    }

    const response = {
      success: createdOrders.length > 0,
      orders_created: createdOrders.length,
      orders: createdOrders,
      errors: errors.length > 0 ? errors : undefined,
    };

    console.log(
      `✅ Supplier order creation completed: ${createdOrders.length} orders created, ${errors.length} errors`
    );

    res.json(response);
  } catch (error) {
    console.error("❌ Error creating supplier orders:", error);
    res.status(500).json({
      type: "server_error",
      message:
        error instanceof Error
          ? error.message
          : "Failed to create supplier orders",
    });
  }
};
