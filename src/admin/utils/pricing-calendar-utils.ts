import { format, parseISO, isSameDay, isWithinInterval } from "date-fns";

export type WeekdayPrices = {
  mon: number;
  tue: number;
  wed: number;
  thu: number;
  fri: number;
  sat: number;
  sun: number;
};

export type WeekdayCosts = {
  mon: number | null;
  tue: number | null;
  wed: number | null;
  thu: number | null;
  fri: number | null;
  sat: number | null;
  sun: number | null;
};

export type WeekdayMargins = {
  mon: number | null;
  tue: number | null;
  wed: number | null;
  thu: number | null;
  fri: number | null;
  sat: number | null;
  sun: number | null;
};

export type BasePriceRule = {
  id: string;
  room_config_id: string;
  occupancy_type_id: string;
  meal_plan_id: string | null;
  weekday_prices: WeekdayPrices;
  currency_code: string;
  // Cost and margin data
  default_gross_cost?: number | null;
  default_fixed_margin?: number | null;
  default_margin_percentage?: number | null;
  weekday_costs?: WeekdayCosts;
  weekday_fixed_margins?: WeekdayMargins;
  weekday_margin_percentages?: WeekdayMargins;
};

export type SeasonalPriceRule = {
  id: string;
  base_price_rule_id: string;
  start_date: string;
  end_date: string;
  amount?: number; // Legacy field for backward compatibility
  currency_code: string;
  priority?: number;
  name?: string;
  // Enhanced fields to match actual API response
  weekday_prices?: {
    mon: number;
    tue: number;
    wed: number;
    thu: number;
    fri: number;
    sat: number;
    sun: number;
  };
  default_values?: {
    gross_cost: number;
    fixed_margin: number;
    margin_percentage: number;
    total: number;
  };
  weekday_values?: {
    mon?: { gross_cost: number; fixed_margin: number; margin_percentage: number; };
    tue?: { gross_cost: number; fixed_margin: number; margin_percentage: number; };
    wed?: { gross_cost: number; fixed_margin: number; margin_percentage: number; };
    thu?: { gross_cost: number; fixed_margin: number; margin_percentage: number; };
    fri?: { gross_cost: number; fixed_margin: number; margin_percentage: number; };
    sat?: { gross_cost: number; fixed_margin: number; margin_percentage: number; };
    sun?: { gross_cost: number; fixed_margin: number; margin_percentage: number; };
  };
};

export type PricingContext = {
  roomConfigId: string;
  occupancyTypeId: string;
  mealPlanId: string | null;
};

export type DailyPricingInfo = {
  date: Date;
  cost: number | null;
  fixedMargin: number | null;
  marginPercentage: number | null;
  price: number;
  isSeasonalOverride: boolean;
  seasonalRuleName?: string;
};

/**
 * Get the day of week key for a given date
 */
export function getDayOfWeekKey(date: Date): keyof WeekdayPrices {
  const dayIndex = date.getDay(); // 0 = Sunday, 1 = Monday, etc.
  const dayKeys: (keyof WeekdayPrices)[] = ['sun', 'mon', 'tue', 'wed', 'thu', 'fri', 'sat'];
  return dayKeys[dayIndex];
}

/**
 * Find the base price rule for a given pricing context
 */
export function findBasePriceRule(
  basePriceRules: BasePriceRule[],
  context: PricingContext
): BasePriceRule | null {
  return basePriceRules.find(rule => 
    rule.room_config_id === context.roomConfigId &&
    rule.occupancy_type_id === context.occupancyTypeId &&
    rule.meal_plan_id === context.mealPlanId
  ) || null;
}

/**
 * Find all seasonal price rules that apply to a specific date and context
 */
export function findApplicableSeasonalRules(
  seasonalRules: SeasonalPriceRule[],
  date: Date,
  basePriceRuleId: string
): SeasonalPriceRule[] {
  return seasonalRules
    .filter(rule => 
      rule.base_price_rule_id === basePriceRuleId &&
      isWithinInterval(date, {
        start: parseISO(rule.start_date),
        end: parseISO(rule.end_date)
      })
    )
    .sort((a, b) => b.priority - a.priority); // Higher priority first
}

/**
 * Calculate the price for a specific date and pricing context
 */
export function calculateDailyPrice(
  date: Date,
  context: PricingContext,
  basePriceRules: BasePriceRule[],
  seasonalRules: SeasonalPriceRule[]
): number {
  // Find the base price rule
  const basePriceRule = findBasePriceRule(basePriceRules, context);
  if (!basePriceRule) {
    return 0; // No base price rule found
  }

  // Get the base price for this day of week
  const dayOfWeek = getDayOfWeekKey(date);
  const basePrice = basePriceRule.weekday_prices[dayOfWeek];

  // Check for seasonal overrides
  const applicableSeasonalRules = findApplicableSeasonalRules(
    seasonalRules,
    date,
    basePriceRule.id
  );

  // Return the highest priority seasonal price, or base price if no seasonal rules
  if (applicableSeasonalRules.length > 0) {
    const seasonalRule = applicableSeasonalRules[0];
    // Use weekday_prices if available, otherwise fall back to amount
    if (seasonalRule.weekday_prices) {
      return seasonalRule.weekday_prices[dayOfWeek] || 0;
    } else {
      return seasonalRule.amount || 0;
    }
  }

  return basePrice;
}

/**
 * Calculate comprehensive daily pricing information including cost, margin, and price
 */
export function calculateDailyPricingInfo(
  date: Date,
  context: PricingContext,
  basePriceRules: BasePriceRule[],
  seasonalRules: SeasonalPriceRule[]
): DailyPricingInfo {
  // Find the base price rule for this context
  const basePriceRule = findBasePriceRule(basePriceRules, context);
  if (!basePriceRule) {
    return {
      date,
      cost: null,
      fixedMargin: null,
      marginPercentage: null,
      price: 0,
      isSeasonalOverride: false,
    };
  }

  // Get the day of the week
  const dayOfWeek = getDayOfWeekKey(date);

  // Get cost and margin data for this day
  let cost = basePriceRule.weekday_costs?.[dayOfWeek] ?? basePriceRule.default_gross_cost ?? null;
  let fixedMargin = basePriceRule.weekday_fixed_margins?.[dayOfWeek] ?? basePriceRule.default_fixed_margin ?? null;
  let marginPercentage = basePriceRule.weekday_margin_percentages?.[dayOfWeek] ?? basePriceRule.default_margin_percentage ?? null;

  // Start with the base price for this day
  let price = basePriceRule.weekday_prices[dayOfWeek] || 0;
  let isSeasonalOverride = false;
  let seasonalRuleName: string | undefined;

  // Check for seasonal overrides
  const applicableSeasonalRules = findApplicableSeasonalRules(
    seasonalRules,
    date,
    basePriceRule.id
  );

  // Apply the highest priority seasonal rule if any
  if (applicableSeasonalRules.length > 0) {
    const seasonalRule = applicableSeasonalRules[0];
    isSeasonalOverride = true;
    seasonalRuleName = seasonalRule.name;

    // Use weekday_prices if available, otherwise fall back to amount
    if (seasonalRule.weekday_prices) {
      price = seasonalRule.weekday_prices[dayOfWeek] || 0;
    } else {
      price = seasonalRule.amount || 0;
    }

    // Override cost and margin data if available in seasonal rule
    if (seasonalRule.weekday_values?.[dayOfWeek]) {
      const seasonalDayValues = seasonalRule.weekday_values[dayOfWeek];
      if (seasonalDayValues.gross_cost !== undefined && seasonalDayValues.gross_cost !== 0) {
        cost = seasonalDayValues.gross_cost;
      }
      if (seasonalDayValues.fixed_margin !== undefined && seasonalDayValues.fixed_margin !== 0) {
        fixedMargin = seasonalDayValues.fixed_margin;
      }
      if (seasonalDayValues.margin_percentage !== undefined && seasonalDayValues.margin_percentage !== 0) {
        marginPercentage = seasonalDayValues.margin_percentage;
      }
    } else if (seasonalRule.default_values) {
      // Use default values if no specific weekday values
      if (seasonalRule.default_values.gross_cost !== undefined && seasonalRule.default_values.gross_cost !== 0) {
        cost = seasonalRule.default_values.gross_cost;
      }
      if (seasonalRule.default_values.fixed_margin !== undefined && seasonalRule.default_values.fixed_margin !== 0) {
        fixedMargin = seasonalRule.default_values.fixed_margin;
      }
      if (seasonalRule.default_values.margin_percentage !== undefined && seasonalRule.default_values.margin_percentage !== 0) {
        marginPercentage = seasonalRule.default_values.margin_percentage;
      }
    }
  }

  // Ensure price is always a number
  price = Number(price) || 0;

  return {
    date,
    cost,
    fixedMargin,
    marginPercentage,
    price,
    isSeasonalOverride,
    seasonalRuleName,
  };
}

/**
 * Calculate prices for a range of dates
 */
export function calculatePricesForDateRange(
  startDate: Date,
  endDate: Date,
  context: PricingContext,
  basePriceRules: BasePriceRule[],
  seasonalRules: SeasonalPriceRule[]
): Map<string, number> {
  const prices = new Map<string, number>();

  const currentDate = new Date(startDate);
  while (currentDate <= endDate) {
    const dateKey = format(currentDate, 'yyyy-MM-dd');
    const price = calculateDailyPrice(currentDate, context, basePriceRules, seasonalRules);
    prices.set(dateKey, price);

    currentDate.setDate(currentDate.getDate() + 1);
  }

  return prices;
}

/**
 * Generate a seasonal price rule for a specific date
 */
export function createSeasonalRuleForDate(
  date: Date,
  price: number,
  basePriceRuleId: string,
  currencyCode: string,
  priority: number = 100
): Omit<SeasonalPriceRule, 'id'> {
  const dateStr = format(date, 'yyyy-MM-dd');

  return {
    base_price_rule_id: basePriceRuleId,
    start_date: dateStr,
    end_date: dateStr,
    amount: price,
    currency_code: currencyCode,
    priority,
    name: `Custom price for ${format(date, 'MMM dd, yyyy')}`
  };
}

/**
 * Check if a date-specific price change would conflict with existing seasonal rules
 */
export function checkPriceChangeConflicts(
  date: Date,
  newPrice: number,
  basePriceRuleId: string,
  seasonalRules: SeasonalPriceRule[]
): {
  hasConflicts: boolean;
  conflictingRules: SeasonalPriceRule[];
  suggestedAction: 'create' | 'update' | 'split';
} {
  const applicableRules = findApplicableSeasonalRules(seasonalRules, date, basePriceRuleId);

  if (applicableRules.length === 0) {
    return {
      hasConflicts: false,
      conflictingRules: [],
      suggestedAction: 'create'
    };
  }

  const highestPriorityRule = applicableRules[0];

  // If the rule covers only this date, we can update it
  if (isSameDay(parseISO(highestPriorityRule.start_date), date) &&
      isSameDay(parseISO(highestPriorityRule.end_date), date)) {
    return {
      hasConflicts: false,
      conflictingRules: [highestPriorityRule],
      suggestedAction: 'update'
    };
  }

  // If the rule covers multiple dates, we need to split it
  return {
    hasConflicts: true,
    conflictingRules: [highestPriorityRule],
    suggestedAction: 'split'
  };
}

/**
 * Determine if adjacent dates can be consolidated into a single seasonal rule
 */
export function findConsolidationOpportunities(
  targetDate: Date,
  targetPrice: number,
  basePriceRuleId: string,
  seasonalRules: SeasonalPriceRule[]
): {
  canConsolidate: boolean;
  suggestedStartDate: Date;
  suggestedEndDate: Date;
  rulesToRemove: SeasonalPriceRule[];
} {
  // This is a simplified version - in practice, you'd want more sophisticated logic
  // to find adjacent dates with the same price and consolidate them

  return {
    canConsolidate: false,
    suggestedStartDate: targetDate,
    suggestedEndDate: targetDate,
    rulesToRemove: []
  };
}
