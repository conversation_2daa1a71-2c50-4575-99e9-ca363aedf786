import { Eye, MoreHorizontal, Package, Copy, FileText } from "lucide-react";
import {
  Container,
  Heading,
  Text,
  Button,
  Badge,
  Toaster,
  toast,
  DropdownMenu,
  Checkbox,
  Tabs,
} from "@camped-ai/ui";
import { useMemo, useState } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import {
  useReactTable,
  getCoreRowModel,
  createColumnHelper,
  ColumnDef,
  getFilteredRowModel,
} from "@tanstack/react-table";
import { useRbac } from "../../../hooks/use-rbac";
import { DataTable } from "../../../../components/table/data-table";
import type { Filter } from "../../../../components/table/data-table";

import {
  CreateSupplierOrdersModal,
  type SupplierOrderCreationData,
} from "../../../components/supplier-management/calendar/CreateSupplierOrdersModal";
import { useCreateSupplierOrders } from "../../../hooks/supplier-management/use-create-supplier-orders";
import {
  useOnRequestItems,
  type ConciergeOrderItem,
} from "../../../hooks/supplier-management/use-on-request-items";
import { type BookingAddon } from "../../../hooks/supplier-management/use-booking-addons";
import { useAdminCurrencies } from "../../../hooks/use-admin-currencies";
import {
  formatCurrencyAmount,
  fromSmallestUnit,
} from "../../../utils/currency-utils";

interface SupplierRequestsPageClientProps {
  pageSize?: number;
}

const SupplierRequestsPageClient: React.FC<SupplierRequestsPageClientProps> = ({
  pageSize = 20,
}) => {
  const navigate = useNavigate();
  const location = useLocation();
  const { hasPermission } = useRbac();

  // Get default currency from store settings
  const { defaultCurrency } = useAdminCurrencies();

  // Row selection state
  const [rowSelection, setRowSelection] = useState({});

  // Modal state
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [modalAddons, setModalAddons] = useState<ConciergeOrderItem[]>([]);
  const [isNewOrder, setIsNewOrder] = useState(true);
  const [isAppendMode, setIsAppendMode] = useState(false);

  // Supplier order creation mutation
  const createSupplierOrdersMutation = useCreateSupplierOrders();

  // Get URL parameters for filtering and pagination
  const searchParams = new URLSearchParams(location.search);
  const currentPage = parseInt(searchParams.get("page") || "1");
  const search = searchParams.get("search") || "";
  const status = searchParams.get("status") || "";

  // Tab state - default to client_confirmed as it's the primary focus
  const [activeTab, setActiveTab] = useState(status || "client_confirmed");

  // Handle tab change and update URL
  const handleTabChange = (newTab: string) => {
    setActiveTab(newTab);
    const newSearchParams = new URLSearchParams(location.search);
    if (newTab === "client_confirmed") {
      // Remove status param for default tab
      newSearchParams.delete("status");
    } else {
      newSearchParams.set("status", newTab);
    }
    // Reset to first page when changing tabs
    newSearchParams.set("page", "1");
    navigate(`${location.pathname}?${newSearchParams.toString()}`, {
      replace: true,
    });
  };

  // Fetch on-request items (ConciergeOrderItem) data based on active tab
  const { data: onRequestData, isLoading } = useOnRequestItems({
    search: search,
    status: activeTab === "all" ? undefined : (activeTab as any),
    exclude_completed: activeTab === "all" ? false : undefined, // Show all when "all" tab is selected
    limit: pageSize,
    offset: (currentPage - 1) * pageSize,
  });

  // Transform ConciergeOrderItem to BookingAddon format for API compatibility
  const transformConciergeOrderItemToBookingAddon = (
    item: ConciergeOrderItem
  ): BookingAddon => {
    return {
      id: item.id,
      order_id: item.concierge_order_id,
      add_on_id: item.variant_id || item.id,
      add_on_name: item.title,
      quantity: item.quantity,
      unit_price: item.unit_price,
      total_price: item.unit_price * item.quantity,
      customer_field_responses: {},
      supplier_order_id: undefined,
      order_status: "pending",
      created_at: item.created_at,
      updated_at: item.updated_at,
      order: undefined,
      add_on: {
        id: item.id,
        name: item.title,
        description: undefined,
        selling_price: item.unit_price,
        selling_currency: "USD",
        metadata: item.metadata as any,
      },
    };
  };

  // Get on-request items data
  const onRequestItems = onRequestData?.concierge_order_items || [];
  const totalCount = onRequestData?.count || 0;

  // Get current page from URL
  const currentPageFromUrl = parseInt(searchParams.get("page") || "1");

  // Column helper for type safety
  const columnHelper = createColumnHelper<ConciergeOrderItem>();

  // Helper functions
  const normalizeStatus = (status: string) => {
    // Convert display status to internal status format
    return status.toLowerCase().replace(/\s+/g, "_");
  };

  // Handle opening modal for bulk order creation
  const handleBulkCreateOrders = () => {
    const selectedIds = Object.keys(rowSelection);
    const selectedItems = onRequestItems.filter((item) =>
      selectedIds.includes(item.id)
    );
    setModalAddons(selectedItems);
    setIsNewOrder(true);
    setIsAppendMode(false);
    setIsModalOpen(true);
  };

  // Handle opening modal for append to existing order
  const handleBulkAppendToExisting = () => {
    const selectedIds = Object.keys(rowSelection);
    const selectedItems = onRequestItems.filter((item) =>
      selectedIds.includes(item.id)
    );
    setModalAddons(selectedItems);
    setIsNewOrder(false);
    setIsAppendMode(true);
    setIsModalOpen(true);
  };

  // Handle modal close
  const handleModalClose = () => {
    setIsModalOpen(false);
    setModalAddons([]);
    setIsAppendMode(false);
  };

  // Handle order creation from modal
  const handleCreateOrders = async (orderData: SupplierOrderCreationData[]) => {
    try {
      // Transform the data to match the API expected format
      const supplierGroups = orderData.map((order) => ({
        supplier_id: order.supplier_id,
        supplier_name: order.supplier_name,
        addons: order.items
          .map((item) => {
            const originalAddon = modalAddons.find(
              (addon) => addon.id === item.addon_id
            );
            return originalAddon
              ? transformConciergeOrderItemToBookingAddon(originalAddon)
              : null;
          })
          .filter(Boolean) as BookingAddon[],
        order_name: order.order_name,
        append_to_existing_order: order.append_to_existing_order,
        delivery_date: order.delivery_date,
        notes: order.notes,
      }));

      await createSupplierOrdersMutation.mutateAsync({
        supplier_groups: supplierGroups,
      });

      // Close modal and clear selection
      handleModalClose();
      setRowSelection({});

      toast.success("Supplier orders created successfully!");
    } catch (error) {
      console.error("Failed to create supplier orders:", error);
      toast.error("Failed to create supplier orders");
    }
  };

  // Format currency using the default currency from store settings
  const formatCurrency = (amount: number) => {
    const currency = defaultCurrency || {
      currency_code: "GBP",
      decimal_digits: 2,
    };

    // Convert from smallest unit (cents) to display unit
    const displayAmount = fromSmallestUnit(amount, currency);

    return formatCurrencyAmount(displayAmount, currency, {
      showSymbol: true,
      showCode: false,
      symbolPosition: "before",
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  // Define filters (removed status filter since we're using tabs)
  const filters: Filter[] = [
    {
      key: "is_active",
      label: "Active Status",
      type: "select",
      options: [
        { label: "All Items", value: "" },
        { label: "Active Only", value: "true" },
        { label: "Inactive Only", value: "false" },
      ],
    },
  ];

  // Define order by options
  const orderBy = [
    {
      label: "Item Title",
      value: "title",
      key: "title" as keyof ConciergeOrderItem,
    },
    {
      label: "Concierge Order ID",
      value: "concierge_order_id",
      key: "concierge_order_id" as keyof ConciergeOrderItem,
    },
    {
      label: "Status",
      value: "status",
      key: "status" as keyof ConciergeOrderItem,
    },
    {
      label: "Unit Price",
      value: "unit_price",
      key: "unit_price" as keyof ConciergeOrderItem,
    },
    {
      label: "Quantity",
      value: "quantity",
      key: "quantity" as keyof ConciergeOrderItem,
    },
    {
      label: "Created Date",
      value: "created_at",
      key: "created_at" as keyof ConciergeOrderItem,
    },
    {
      label: "Updated Date",
      value: "updated_at",
      key: "updated_at" as keyof ConciergeOrderItem,
    },
  ];

  // Define columns
  const columns = useMemo<ColumnDef<ConciergeOrderItem, any>[]>(
    () => [
      // Select column for checkboxes
      columnHelper.display({
        id: "select",
        header: ({ table }) => (
          <Checkbox
            checked={table.getIsAllPageRowsSelected()}
            onCheckedChange={(value) =>
              table.toggleAllPageRowsSelected(!!value)
            }
          />
        ),
        cell: ({ row }) => (
          <Checkbox
            checked={row.getIsSelected()}
            disabled={!row.getCanSelect()}
            onCheckedChange={(value) => row.toggleSelected(!!value)}
          />
        ),
        enableSorting: false,
        enableHiding: false,
      }),
      columnHelper.display({
        id: "item",
        header: "On-Request Item",
        cell: ({ row }) => {
          const item = row.original;
          return (
            <div className="flex items-center gap-x-3 truncate">
              <div className="flex p-2 items-center justify-center rounded bg-ui-bg-subtle">
                <Package className="h-4 w-4 text-ui-fg-subtle" />
              </div>
              <div>
                <Text className="truncate w-[200px] text-wrap" weight="plus">
                  {item.title}
                </Text>
              </div>
            </div>
          );
        },
      }),
      // Enhanced Column: Hotel
      columnHelper.display({
        id: "hotel",
        header: "Hotel",
        cell: ({ row }) => {
          const item = row.original;
          return (
            <div className="w-[150px] truncate">
              <Text className="txt-compact-medium">
                {item.hotel || "Not specified"}
              </Text>
            </div>
          );
        },
      }),
      columnHelper.display({
        id: "category",
        header: "Category",
        cell: ({ row }) => {
          const item = row.original;
          const categoryName = item.category?.name || "Uncategorized";
          const categoryIcon = item.category?.icon;
          return (
            <div className="w-[140px]">
              <div className="flex items-center gap-1">
                {categoryIcon && (
                  <span className="text-sm">{categoryIcon}</span>
                )}
                <span>{categoryName}</span>
              </div>
            </div>
          );
        },
      }),
      columnHelper.display({
        id: "pricing",
        header: "Unit Price & Total",
        cell: ({ row }) => {
          const item = row.original;
          // Use enhanced total_price calculation from API
          const unitPrice = item.unit_price;
          const quantity = item.quantity;
          const totalPrice = item.total_price || unitPrice * quantity;
          return (
            <div className="w-[120px] text-right">
              <Text className="txt-compact-medium-plus" weight="plus">
                {formatCurrency(totalPrice)}
              </Text>
              <Text className="txt-compact-small text-ui-fg-muted">
                {formatCurrency(unitPrice)} × {quantity}
              </Text>
            </div>
          );
        },
      }),
      // Enhanced Column: Customer
      columnHelper.display({
        id: "customer",
        header: "Customer",
        cell: ({ row }) => {
          const item = row.original;
          return (
            <div className="w-[150px] truncate">
              <Text className="txt-compact-medium">
                {item.customer || "Unknown Customer"}
              </Text>
            </div>
          );
        },
      }),
      // Enhanced Column: Check-in Date
      columnHelper.display({
        id: "check_in_date",
        header: "Check-in Date",
        cell: ({ row }) => {
          const item = row.original;
          return (
            <div className="w-[120px]">
              <Text className="txt-compact-medium">
                {item.check_in_date
                  ? formatDate(item.check_in_date)
                  : "Not specified"}
              </Text>
            </div>
          );
        },
      }),
      // Enhanced Column: Check-out Date
      columnHelper.display({
        id: "check_out_date",
        header: "Check-out Date",
        cell: ({ row }) => {
          const item = row.original;
          return (
            <div className="w-[120px]">
              <Text className="txt-compact-medium">
                {item.check_out_date
                  ? formatDate(item.check_out_date)
                  : "Not specified"}
              </Text>
            </div>
          );
        },
      }),
      columnHelper.display({
        id: "dates",
        header: "Dates",
        cell: ({ row }) => {
          const item = row.original;
          return (
            <div className="w-[140px]">
              <Text className="txt-compact-medium">
                {formatDate(item.added_at)}
              </Text>
              {item.finalized_at && (
                <Text className="txt-compact-small text-ui-fg-muted">
                  Finalized: {formatDate(item.finalized_at)}
                </Text>
              )}
            </div>
          );
        },
      }),
      columnHelper.display({
        id: "concierge_order",
        header: "Concierge Order",
        cell: ({ row }) => {
          const item = row.original;
          return (
            <div className="w-[140px] truncate">
              <Text className="txt-compact-medium-plus" weight="plus">
                {item.concierge_order_id}
              </Text>
              {item.added_by && (
                <Text className="txt-compact-small text-ui-fg-muted">
                  Added by: {item.added_by}
                </Text>
              )}
            </div>
          );
        },
      }),
      columnHelper.display({
        id: "service_dates",
        header: "Service Dates",
        cell: ({ row }) => {
          const item = row.original;
          return (
            <div className="w-[140px]">
              {item.start_date && item.end_date ? (
                <>
                  <Text className="txt-compact-medium">
                    {formatDate(item.start_date)}
                  </Text>
                  <Text className="txt-compact-small text-ui-fg-muted">
                    to {formatDate(item.end_date)}
                  </Text>
                </>
              ) : item.start_date ? (
                <Text className="txt-compact-medium">
                  {formatDate(item.start_date)}
                </Text>
              ) : (
                <Text className="txt-compact-medium text-ui-fg-muted">
                  No dates set
                </Text>
              )}
            </div>
          );
        },
      }),
      columnHelper.display({
        id: "created_at",
        header: "Created",
        cell: ({ row }) => {
          const item = row.original;
          return (
            <div className="w-[100px]">
              <Text className="txt-compact-medium">
                {formatDate(item.created_at)}
              </Text>
            </div>
          );
        },
      }),
      columnHelper.display({
        id: "actions",
        header: "",
        cell: ({ row }) => {
          const item = row.original;
          return (
            <DropdownMenu>
              <DropdownMenu.Trigger asChild>
                <Button variant="transparent" size="small">
                  <MoreHorizontal className="h-4 w-4" />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content>
                <DropdownMenu.Item
                  onClick={() =>
                    navigate(`/supplier-management/requests/${item.id}`)
                  }
                >
                  <Eye className="h-4 w-4 mr-2" />
                  View Details
                </DropdownMenu.Item>
                {item.metadata && Object.keys(item.metadata).length > 0 && (
                  <DropdownMenu.Item
                    onClick={() => {
                      toast.info("Item metadata available");
                    }}
                  >
                    <FileText className="h-4 w-4 mr-2" />
                    View Metadata
                  </DropdownMenu.Item>
                )}
                <DropdownMenu.Separator />
              </DropdownMenu.Content>
            </DropdownMenu>
          );
        },
      }),
    ],
    [navigate, hasPermission]
  );

  // Create table instance
  const table = useReactTable({
    data: onRequestItems,
    columns,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    enableRowSelection: true,
    getRowId: (row) => row.id,
    manualPagination: true,
    pageCount: Math.ceil(totalCount / pageSize),
    state: {
      pagination: {
        pageIndex: currentPageFromUrl - 1,
        pageSize,
      },
      rowSelection,
    },
    onRowSelectionChange: setRowSelection,
  });

  // Override table pagination methods to update URL
  table.nextPage = () => {
    const newPage = currentPageFromUrl + 1;
    if (newPage <= Math.ceil(totalCount / pageSize)) {
      const newSearchParams = new URLSearchParams(location.search);
      newSearchParams.set("page", newPage.toString());
      navigate(`${location.pathname}?${newSearchParams.toString()}`, {
        replace: true,
      });
    }
  };

  table.previousPage = () => {
    const newPage = currentPageFromUrl - 1;
    if (newPage >= 1) {
      const newSearchParams = new URLSearchParams(location.search);
      newSearchParams.set("page", newPage.toString());
      navigate(`${location.pathname}?${newSearchParams.toString()}`, {
        replace: true,
      });
    }
  };

  table.setPageIndex = (updater: any) => {
    const pageIndex =
      typeof updater === "function" ? updater(currentPageFromUrl - 1) : updater;
    const newPage = pageIndex + 1;
    const newSearchParams = new URLSearchParams(location.search);
    newSearchParams.set("page", newPage.toString());
    navigate(`${location.pathname}?${newSearchParams.toString()}`, {
      replace: true,
    });
  };

  return (
    <Container className="divide-y p-0">
      {/* Header */}
      <div className="flex items-center justify-between px-6 py-4">
        <div>
          <Heading level="h2">On Requests</Heading>
          <Text className="text-ui-fg-muted">Concierge Order Items</Text>
        </div>
        <div className="flex items-center gap-x-2">
          {hasPermission("supplier-orders:create") &&
            Object.keys(rowSelection).length > 0 && (
              <>
                <Button
                  size="small"
                  variant="secondary"
                  onClick={handleBulkCreateOrders}
                  disabled={createSupplierOrdersMutation.isPending}
                >
                  {createSupplierOrdersMutation.isPending
                    ? "Creating..."
                    : `Create Orders (${Object.keys(rowSelection).length})`}
                </Button>
                <Button
                  size="small"
                  variant="transparent"
                  onClick={handleBulkAppendToExisting}
                  disabled={createSupplierOrdersMutation.isPending}
                >
                  Append to Existing
                </Button>
              </>
            )}
        </div>
      </div>

      {/* Status Tabs */}
      <div className="px-6 py-4 border-b">
        <Tabs value={activeTab} onValueChange={handleTabChange}>
          <Tabs.List>
            <Tabs.Trigger value="client_confirmed" className="relative">
              Client Confirmed
              <Badge size="small" className="ml-2 bg-green-100 text-green-800">
                Action Required
              </Badge>
            </Tabs.Trigger>
            <Tabs.Trigger value="under_review">Under Review</Tabs.Trigger>
            <Tabs.Trigger value="order_placed">Order Placed</Tabs.Trigger>
            <Tabs.Trigger value="cancelled">Cancelled</Tabs.Trigger>
            <Tabs.Trigger value="completed">Completed</Tabs.Trigger>
            <Tabs.Trigger value="all">All Items</Tabs.Trigger>
          </Tabs.List>
        </Tabs>
      </div>

      {/* DataTable */}
      <DataTable
        table={table}
        columns={columns}
        pageSize={pageSize}
        count={totalCount}
        isLoading={isLoading}
        filters={filters}
        orderBy={orderBy}
        search="autofocus"
        pagination
        navigateTo={(row) => `/supplier-management/requests/${row.original.id}`}
        queryObject={{ ...Object.fromEntries(searchParams) }}
        noRecords={{
          title: "No on-request items found",
          message: "Booked add-ons and on-request items will appear here",
        }}
      />

      {/* Create Supplier Orders Modal */}
      <CreateSupplierOrdersModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        addons={modalAddons as any}
        onCreateOrders={handleCreateOrders}
        isNewOrder={isNewOrder}
        isAppendMode={isAppendMode}
      />

      <Toaster />
    </Container>
  );
};

export default SupplierRequestsPageClient;
