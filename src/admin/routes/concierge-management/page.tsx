import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { Container, Heading, Text } from "@camped-ai/ui";
import { Calendar, CheckSquare, MapPin } from "lucide-react";
import { useNavigate } from "react-router-dom";
import PermissionBasedSidebarHider from "../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../components/rbac/RoleGuard";

const ConciergeManagementPage = () => {
  const navigate = useNavigate();

  // Core Functions Cards - matching supplier management structure
  const coreFunctionCards = [
    {
      title: "Bookings",
      description: "View and manage all hotel bookings",
      icon: Calendar,
      count: 0, // TODO: Get actual count
      href: "/concierge-management/bookings",
      isPlaceholder: false,
    },
    {
      title: "Tasks",
      description: "Comprehensive task management system",
      icon: CheckSquare,
      count: 0, // TODO: Get actual count
      href: "/concierge-management/tasks",
      isPlaceholder: false,
    },
    {
      title: "Itineraries",
      description: "Manage guest itineraries and schedules",
      icon: MapPin,
      count: 0, // TODO: Get actual count
      href: "/concierge-management/itineraries",
      isPlaceholder: false,
    },
  ];

  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="concierge_management:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to access Concierge Management.
              </Text>
            </div>
          </Container>
        }
      >
        <Container className="divide-y p-0">
          <div className="flex items-center justify-between px-6 py-4">
            <div>
              <Heading level="h2">Concierge Management</Heading>
            </div>
          </div>

          {/* Core Functions Section */}
          <div className="px-6 py-6">
            <div className="mb-6">
              <Heading level="h3">Core Functions</Heading>
              <Text className="text-ui-fg-subtle">
                Primary concierge management operations and data
              </Text>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {coreFunctionCards.map((card) => (
                <div
                  key={card.title}
                  className={`p-5 border rounded-lg transition-all duration-200 ${
                    card.isPlaceholder
                      ? "bg-gray-50 border-gray-200 opacity-60 cursor-not-allowed"
                      : "bg-white border-gray-200 hover:shadow-lg hover:border-blue-300 cursor-pointer group"
                  }`}
                  onClick={() => !card.isPlaceholder && navigate(card.href)}
                >
                  <div className="flex items-start gap-4">
                    <div
                      className={`p-2 rounded-lg ${
                        card.isPlaceholder
                          ? "bg-gray-100"
                          : "bg-blue-50 group-hover:bg-blue-100"
                      } transition-colors`}
                    >
                      <card.icon className="h-6 w-6 text-blue-600 opacity-50" />
                    </div>
                    <div className="flex-1">
                      <div
                        className={`font-semibold ${
                          card.isPlaceholder
                            ? "text-gray-500"
                            : "text-gray-900 group-hover:text-blue-700"
                        } transition-colors flex items-center gap-2`}
                      >
                        {card.title}
                      </div>
                      <div
                        className={`text-sm mt-1 leading-relaxed ${
                          card.isPlaceholder ? "text-gray-400" : "text-gray-600"
                        }`}
                      >
                        {card.description}
                      </div>
                      {!card.isPlaceholder && (
                        <div className="mt-3 flex items-center justify-between">
                          <span className="text-xs text-blue-600 font-medium group-hover:text-blue-700">
                            View all →
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </Container>
      </RoleGuard>
    </>
  );
};



export const config = defineRouteConfig({
  label: "Concierge Management",
});

export default ConciergeManagementPage;
