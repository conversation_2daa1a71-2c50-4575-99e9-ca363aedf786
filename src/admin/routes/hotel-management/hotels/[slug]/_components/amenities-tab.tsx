import React, { useState } from "react";
import { Container, <PERSON>ing, Text, Badge, Button, Table, FocusModal } from "@camped-ai/ui";
import { Shield, Wifi, Car, Coffee, Dumbbell, Waves, Utensils, Tv, AlertTriangle, BookOpen, Clock, XCircle, Plus, Edit, Trash2, Calendar, CheckCircle, X } from "lucide-react";
import { HotelData, CancellationPolicyData } from "../../../../../types";
import CancellationPolicyForm from "../../../../../components/hotel/cancellation-policy/cancellation-policy-form";
import {
  useAdminCreateCancellationPolicy,
  useAdminUpdateCancellationPolicy,
  useAdminDeleteCancellationPolicy,
  useAdminCancellationPolicies
} from "../../../../../hooks/cancellation-policies/use-admin-cancellation-policies";
import { useRbac } from "../../../../../hooks/use-rbac";

interface AmenitiesTabProps {
  hotel: HotelData | null;
}

const AmenitiesTab: React.FC<AmenitiesTabProps> = ({ hotel }) => {
  const { hasPermission } = useRbac();
  const [isAddPolicyOpen, setIsAddPolicyOpen] = useState(false);
  const [isEditPolicyOpen, setIsEditPolicyOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [selectedPolicy, setSelectedPolicy] = useState<CancellationPolicyData | null>(null);

  // API hooks
  const { data: cancellationPoliciesData, isLoading: isPoliciesLoading } = useAdminCancellationPolicies(hotel?.id);
  const createPolicyMutation = useAdminCreateCancellationPolicy();
  const updatePolicyMutation = useAdminUpdateCancellationPolicy(selectedPolicy?.id || "");
  const deletePolicyMutation = useAdminDeleteCancellationPolicy();

  // Use the fetched data instead of hotel.cancellation_policies
  const cancellationPolicies = cancellationPoliciesData?.cancellation_policies || [];

  if (!hotel) {
    return (
      <Container className="p-6">
        <Text className="text-muted-foreground">Loading hotel details...</Text>
      </Container>
    );
  }

  // Debug logging to help troubleshoot
  console.log("Hotel data in AmenitiesTab:", hotel);
  console.log("Cancellation policies:", cancellationPolicies);

  const handleAddPolicy = () => {
    setSelectedPolicy(null);
    setIsAddPolicyOpen(true);
  };

  const handleEditPolicy = (policy: CancellationPolicyData) => {
    setSelectedPolicy(policy);
    setIsEditPolicyOpen(true);
  };

  const handleDeletePolicy = (policy: CancellationPolicyData) => {
    setSelectedPolicy(policy);
    setIsDeleteDialogOpen(true);
  };

  const handleSavePolicy = async (data: Partial<CancellationPolicyData>) => {
    try {
      if (selectedPolicy) {
        // Update existing policy
        await updatePolicyMutation.mutateAsync(data);
      } else {
        // Create new policy
        await createPolicyMutation.mutateAsync({
          ...data,
          hotel_id: hotel.id,
        } as any);
      }

      setIsAddPolicyOpen(false);
      setIsEditPolicyOpen(false);
      setSelectedPolicy(null);

      // React Query will automatically update the UI through cache invalidation
    } catch (error) {
      console.error("Error saving policy:", error);
      // Error handling is done in the mutation hooks
    }
  };

  const confirmDelete = async () => {
    if (!selectedPolicy) return;

    try {
      await deletePolicyMutation.mutateAsync({
        id: selectedPolicy.id,
        hotelId: hotel.id,
      });

      // Close the dialog and clear selection
      setIsDeleteDialogOpen(false);
      setSelectedPolicy(null);

      // React Query will automatically update the UI through cache invalidation
      // The table row will be removed automatically without page refresh
    } catch (error) {
      // Error handling is done in the mutation hook
    }
  };

  // Icon mapping for common amenities
  const getAmenityIcon = (amenity: string) => {
    const amenityLower = amenity.toLowerCase();
    if (amenityLower.includes('wifi') || amenityLower.includes('internet')) return Wifi;
    if (amenityLower.includes('parking') || amenityLower.includes('car')) return Car;
    if (amenityLower.includes('coffee') || amenityLower.includes('breakfast')) return Coffee;
    if (amenityLower.includes('gym') || amenityLower.includes('fitness')) return Dumbbell;
    if (amenityLower.includes('pool') || amenityLower.includes('swimming')) return Waves;
    if (amenityLower.includes('restaurant') || amenityLower.includes('dining')) return Utensils;
    if (amenityLower.includes('tv') || amenityLower.includes('television')) return Tv;
    return Shield; // Default icon
  };

  const getAmenityColor = (index: number) => {
    const colors = [
      'bg-blue-100 dark:bg-blue-900/50 text-blue-600 dark:text-blue-400',
      'bg-green-100 dark:bg-green-900/50 text-green-600 dark:text-green-400',
      'bg-purple-100 dark:bg-purple-900/50 text-purple-600 dark:text-purple-400',
      'bg-orange-100 dark:bg-orange-900/50 text-orange-600 dark:text-orange-400',
      'bg-pink-100 dark:bg-pink-900/50 text-pink-600 dark:text-pink-400',
      'bg-indigo-100 dark:bg-indigo-900/50 text-indigo-600 dark:text-indigo-400',
    ];
    return colors[index % colors.length];
  };

  return (
    <div className="space-y-6">
      {/* Hotel Facilities Container */}
      <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border border-border">
        <div className="px-6 py-4 border-b border-border flex items-center justify-between bg-muted/50">
          <div className="flex items-center gap-3">
            <Shield className="w-5 h-5 text-muted-foreground flex-shrink-0" />
            <Heading
              level="h2"
              className="text-lg font-medium text-foreground"
            >
              Hotel Facilities
            </Heading>
          </div>
          {hotel.amenities && hotel.amenities.length > 0 && (
            <Badge variant="secondary" className="text-xs">
              {hotel.amenities.length} facilities
            </Badge>
          )}
        </div>
        <div className="p-6">

          {/* Amenities Grid */}
          {hotel.amenities && hotel.amenities.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {hotel.amenities.map((amenity, index) => {
                  const IconComponent = getAmenityIcon(amenity);
                  const colorClass = getAmenityColor(index);

                  return (
                    <div
                      key={index}
                      className="p-4 bg-card rounded-lg border border-border hover:border-muted-foreground/20 transition-colors"
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-10 h-10 rounded-full flex items-center justify-center flex-shrink-0 ${colorClass}`}>
                          <IconComponent className="w-5 h-5" />
                        </div>
                        <div className="min-w-0 flex-1">
                          <Text className="font-medium text-foreground break-words">
                            {amenity}
                          </Text>
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
                <Shield className="w-8 h-8 text-muted-foreground" />
              </div>
              <Heading level="h3" className="text-lg font-medium text-foreground mb-2">
                No Facilities Listed
              </Heading>
              <Text className="text-muted-foreground max-w-md mx-auto">
                This hotel doesn't have any facilities listed yet. Facilities can be added when editing the hotel details.
              </Text>
            </div>
          )}
        </div>
      </Container>

      {/* Safety Measures Container */}
      {hotel.safety_measures && hotel.safety_measures.length > 0 && (
        <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border border-border">
          <div className="px-6 py-4 border-b border-border flex items-center justify-between bg-muted/50">
            <div className="flex items-center gap-3">
              <AlertTriangle className="w-5 h-5 text-muted-foreground flex-shrink-0" />
              <Heading
                level="h2"
                className="text-lg font-medium text-foreground"
              >
                Safety Measures
              </Heading>
            </div>
            <Badge variant="secondary" className="text-xs">
              {hotel.safety_measures.length} measures
            </Badge>
          </div>
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {hotel.safety_measures.map((measure, index) => (
                  <div
                    key={index}
                    className="p-4 bg-red-50 dark:bg-red-950/30 rounded-lg border border-red-200 dark:border-red-800"
                  >
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 rounded-full bg-red-100 dark:bg-red-900/50 flex items-center justify-center flex-shrink-0">
                        <Shield className="w-4 h-4 text-red-600 dark:text-red-400" />
                      </div>
                      <div className="min-w-0 flex-1">
                        <Text className="font-medium text-red-800 dark:text-red-200 break-words">
                          {measure}
                        </Text>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        </Container>
      )}

      {/* Hotel Rules Container */}
      {hotel.rules && hotel.rules.length > 0 && (
        <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border border-border">
          <div className="px-6 py-4 border-b border-border flex items-center justify-between bg-muted/50">
            <div className="flex items-center gap-3">
              <BookOpen className="w-5 h-5 text-muted-foreground flex-shrink-0" />
              <Heading
                level="h2"
                className="text-lg font-medium text-foreground"
              >
                Hotel Rules
              </Heading>
            </div>
            <Badge variant="secondary" className="text-xs">
              {hotel.rules.length} rules
            </Badge>
          </div>
          <div className="p-6">
            <div className="space-y-3">
                {hotel.rules.map((rule, index) => (
                  <div
                    key={index}
                    className="p-4 bg-yellow-50 dark:bg-yellow-950/30 rounded-lg border border-yellow-200 dark:border-yellow-800"
                  >
                    <Text className="text-yellow-800 dark:text-yellow-200 break-words">
                      {rule}
                    </Text>
                  </div>
                ))}
            </div>
          </div>
        </Container>
      )}

      {/* Booking Rules Container */}
      <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border border-border">
        <div className="px-6 py-4 border-b border-border flex items-center justify-between bg-muted/50">
          <div className="flex items-center gap-3">
            <Calendar className="w-5 h-5 text-muted-foreground flex-shrink-0" />
            <Heading
              level="h2"
              className="text-lg font-medium text-foreground"
            >
              Booking Rules
            </Heading>
          </div>
        </div>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Check-in Time */}
            <div className="p-4 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/50 flex items-center justify-center flex-shrink-0">
                  <Clock className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                </div>
                <Text className="font-medium text-blue-800 dark:text-blue-200">
                  Check-in Time
                </Text>
              </div>
              <Text className="text-blue-700 dark:text-blue-300 text-lg font-semibold ml-11">
                {hotel.check_in_time || "Not specified"}
              </Text>
            </div>

            {/* Check-out Time */}
            <div className="p-4 bg-green-50 dark:bg-green-950/30 rounded-lg border border-green-200 dark:border-green-800">
              <div className="flex items-center gap-3 mb-2">
                <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/50 flex items-center justify-center flex-shrink-0">
                  <Clock className="w-4 h-4 text-green-600 dark:text-green-400" />
                </div>
                <Text className="font-medium text-green-800 dark:text-green-200">
                  Check-out Time
                </Text>
              </div>
              <Text className="text-green-700 dark:text-green-300 text-lg font-semibold ml-11">
                {hotel.check_out_time || "Not specified"}
              </Text>
            </div>

            {/* Pets Allowed */}
            <div className={`p-4 rounded-lg border ${
              hotel.is_pets_allowed
                ? "bg-emerald-50 dark:bg-emerald-950/30 border-emerald-200 dark:border-emerald-800"
                : "bg-red-50 dark:bg-red-950/30 border-red-200 dark:border-red-800"
            }`}>
              <div className="flex items-center gap-3 mb-2">
                <div className={`w-8 h-8 rounded-full flex items-center justify-center flex-shrink-0 ${
                  hotel.is_pets_allowed
                    ? "bg-emerald-100 dark:bg-emerald-900/50"
                    : "bg-red-100 dark:bg-red-900/50"
                }`}>
                  {hotel.is_pets_allowed ? (
                    <CheckCircle className="w-4 h-4 text-emerald-600 dark:text-emerald-400" />
                  ) : (
                    <X className="w-4 h-4 text-red-600 dark:text-red-400" />
                  )}
                </div>
                <Text className={`font-medium ${
                  hotel.is_pets_allowed
                    ? "text-emerald-800 dark:text-emerald-200"
                    : "text-red-800 dark:text-red-200"
                }`}>
                  Pets Allowed
                </Text>
              </div>
              <Text className={`text-lg font-semibold ml-11 ${
                hotel.is_pets_allowed
                  ? "text-emerald-700 dark:text-emerald-300"
                  : "text-red-700 dark:text-red-300"
              }`}>
                {hotel.is_pets_allowed ? "Yes" : "No"}
              </Text>
            </div>
          </div>
        </div>
      </Container>

      {/* Cancellation Policies Container */}
      <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border border-border">
        <div className="px-6 py-4 border-b border-border flex items-center justify-between bg-muted/50">
          <div className="flex items-center gap-3">
            <Clock className="w-5 h-5 text-muted-foreground flex-shrink-0" />
            <Heading
              level="h2"
              className="text-lg font-medium text-foreground"
            >
              Cancellation Policies
            </Heading>
            {cancellationPolicies.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                {cancellationPolicies.length} policies
              </Badge>
            )}
          </div>
          {hasPermission("hotel_management:create") && (
            <Button
              variant="secondary"
              size="small"
              onClick={handleAddPolicy}
              className="flex items-center gap-2"
            >
              <Plus className="w-4 h-4" />
              Add Policy
            </Button>
          )}
        </div>
        <div className="overflow-hidden">
          {isPoliciesLoading ? (
            <div className="text-center py-12">
              <Text className="text-muted-foreground">Loading cancellation policies...</Text>
            </div>
          ) : cancellationPolicies.length > 0 ? (
            <div className="overflow-x-auto">
              <Table>
                <Table.Header>
                  <Table.Row className="border-b border-border">
                    <Table.HeaderCell className="text-left font-medium text-muted-foreground py-3 px-6">
                      Name
                    </Table.HeaderCell>
                    <Table.HeaderCell className="text-left font-medium text-muted-foreground py-3 px-6">
                      Days Before Check-in
                    </Table.HeaderCell>
                    <Table.HeaderCell className="text-left font-medium text-muted-foreground py-3 px-6">
                      Refund
                    </Table.HeaderCell>
                    <Table.HeaderCell className="text-left font-medium text-muted-foreground py-3 px-6">
                      Status
                    </Table.HeaderCell>
                    <Table.HeaderCell className="text-right font-medium text-muted-foreground py-3 px-6">
                      Actions
                    </Table.HeaderCell>
                  </Table.Row>
                </Table.Header>
                <Table.Body>
                  {cancellationPolicies.map((policy, index) => {
                    const getRefundText = () => {
                      switch (policy.refund_type) {
                        case 'percentage':
                          return `${policy.refund_amount}% refund`;
                        case 'fixed':
                          return `$${policy.refund_amount} refund`;
                        case 'no_refund':
                          return 'No refund';
                        default:
                          return 'Unknown refund type';
                      }
                    };

                    const getRefundBadgeColor = () => {
                      switch (policy.refund_type) {
                        case 'percentage':
                          return 'green';
                        case 'fixed':
                          return 'blue';
                        case 'no_refund':
                          return 'red';
                        default:
                          return 'grey';
                      }
                    };

                    return (
                      <Table.Row
                        key={index}
                        className="border-b border-border hover:bg-muted/50 transition-colors"
                      >
                        <Table.Cell className="py-4 px-6">
                          <div className="space-y-1">
                            <Text className="font-medium text-foreground">
                              {policy.name}
                            </Text>
                            {policy.description && (
                              <Text className="text-sm text-muted-foreground line-clamp-2">
                                {policy.description}
                              </Text>
                            )}
                          </div>
                        </Table.Cell>
                        <Table.Cell className="py-4 px-6">
                          <Text className="text-foreground font-medium">
                            {policy.days_before_checkin === 0
                              ? 'Same day'
                              : `${policy.days_before_checkin} days`
                            }
                          </Text>
                        </Table.Cell>
                        <Table.Cell className="py-4 px-6">
                          <Badge
                            color={getRefundBadgeColor()}
                            className="text-xs font-medium px-2 py-1"
                          >
                            {getRefundText()}
                          </Badge>
                        </Table.Cell>
                        <Table.Cell className="py-4 px-6">
                          <Badge
                            color="green"
                            className="text-xs font-medium px-2 py-1"
                          >
                            Active
                          </Badge>
                        </Table.Cell>
                        <Table.Cell className="py-4 px-6">
                          <div className="flex items-center justify-end gap-2">
                            {hasPermission("hotel_management:edit") && (
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  handleEditPolicy(policy);
                                }}
                                className="h-8 w-8 text-muted-foreground hover:text-foreground flex items-center justify-center rounded-md hover:bg-muted transition-colors"
                              >
                                <Edit className="w-4 h-4" />
                              </button>
                            )}
                            {hasPermission("hotel_management:delete") && (
                              <button
                                type="button"
                                onClick={(e) => {
                                  e.preventDefault();
                                  e.stopPropagation();
                                  handleDeletePolicy(policy);
                                }}
                                className="h-8 w-8 text-muted-foreground hover:text-red-600 flex items-center justify-center rounded-md hover:bg-muted transition-colors"
                              >
                                <Trash2 className="w-4 h-4" />
                              </button>
                            )}
                          </div>
                        </Table.Cell>
                      </Table.Row>
                    );
                  })}
                </Table.Body>
              </Table>
            </div>
          ) : (
            <div className="text-center py-12">
              <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center mx-auto mb-4">
                <Clock className="w-8 h-8 text-muted-foreground" />
              </div>
              <Heading level="h3" className="text-lg font-medium text-foreground mb-2">
                No Cancellation Policies
              </Heading>
              <Text className="text-muted-foreground max-w-md mx-auto">
                This hotel doesn't have any cancellation policies defined yet.
                {hasPermission("hotel_management:create")
                  ? "Policies can be added in the hotel management section."
                  : "Contact an administrator to add cancellation policies."
                }
              </Text>
              {hasPermission("hotel_management:create") && (
                <Button
                  variant="secondary"
                  onClick={handleAddPolicy}
                  className="mt-4"
                >
                  <Plus className="w-4 h-4 mr-2" />
                  Add First Policy
                </Button>
              )}
            </div>
          )}
        </div>
      </Container>

      {/* Add Policy Modal */}
      <FocusModal open={isAddPolicyOpen} onOpenChange={setIsAddPolicyOpen}>
        <FocusModal.Content className="cancellation-policy-modal fixed right-0 top-0 w-[600px] max-w-[90vw] h-full bg-white shadow-2xl overflow-hidden m-0 rounded-none z-[100]">
          <CancellationPolicyForm
            hotelId={hotel.id}
            onSubmit={handleSavePolicy}
            onCancel={() => setIsAddPolicyOpen(false)}
            selectedPolicy={null as any}
          />
        </FocusModal.Content>
      </FocusModal>

      {/* Edit Policy Modal */}
      <FocusModal open={isEditPolicyOpen} onOpenChange={setIsEditPolicyOpen}>
        <FocusModal.Content className="cancellation-policy-modal fixed right-0 top-0 w-[600px] max-w-[90vw] h-full bg-white shadow-2xl overflow-hidden m-0 rounded-none z-[100]">
          {selectedPolicy && (
            <CancellationPolicyForm
              initialData={selectedPolicy}
              hotelId={hotel.id}
              onSubmit={handleSavePolicy}
              onCancel={() => setIsEditPolicyOpen(false)}
              selectedPolicy={selectedPolicy}
            />
          )}
        </FocusModal.Content>
      </FocusModal>

      {/* Delete Confirmation Dialog */}
      {isDeleteDialogOpen && (
        <div className="fixed inset-0 z-[100] flex items-center justify-center">
          {/* Backdrop */}
          <div
            className="absolute inset-0 bg-black/50 backdrop-blur-sm"
            onClick={() => {
              setIsDeleteDialogOpen(false);
              setSelectedPolicy(null);
            }}
          />

          {/* Modal Content */}
          <div className="relative bg-white rounded-lg shadow-xl max-w-md w-full mx-4 overflow-hidden">
            {/* Header */}
            <div className="px-6 py-4 border-b border-gray-200">
              <div className="flex items-center justify-between">
                <h2 className="text-lg font-semibold text-red-600">
                  Delete Cancellation Policy
                </h2>
                <button
                  onClick={() => {
                    setIsDeleteDialogOpen(false);
                    setSelectedPolicy(null);
                  }}
                  className="text-gray-400 hover:text-gray-600 transition-colors"
                >
                  <XCircle className="w-5 h-5" />
                </button>
              </div>
            </div>

            {/* Body */}
            <div className="px-6 py-6">
              <div className="flex items-start gap-4 mb-4">
                <div className="w-12 h-12 rounded-full bg-red-100 flex items-center justify-center flex-shrink-0">
                  <AlertTriangle className="w-6 h-6 text-red-600" />
                </div>
                <div className="flex-1">
                  <h3 className="font-medium text-gray-900 mb-1">
                    Are you sure you want to delete this policy?
                  </h3>
                  {selectedPolicy && (
                    <p className="text-sm text-gray-600 mb-2">
                      Policy: <span className="font-medium">"{selectedPolicy.name}"</span>
                    </p>
                  )}
                  <p className="text-sm text-gray-500">
                    This action cannot be undone. The cancellation policy will be permanently removed.
                  </p>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="px-6 py-4 bg-gray-50 border-t border-gray-200 flex justify-end gap-3">
              <button
                type="button"
                onClick={() => {
                  setIsDeleteDialogOpen(false);
                  setSelectedPolicy(null);
                }}
                disabled={deletePolicyMutation.isPending}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={confirmDelete}
                disabled={deletePolicyMutation.isPending}
                className="px-4 py-2 text-sm font-medium text-white bg-red-600 border border-transparent rounded-md hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center gap-2"
              >
                {deletePolicyMutation.isPending && (
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                )}
                {deletePolicyMutation.isPending ? "Deleting..." : "Delete Policy"}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AmenitiesTab;
