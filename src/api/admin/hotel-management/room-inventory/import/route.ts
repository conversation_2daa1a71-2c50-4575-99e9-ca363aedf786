import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { HOTEL_MODULE } from "../../../../../modules/hotel-management/hotel";
import HotelModuleService from "../../../../../modules/hotel-management/hotel/service";
import { ROOM_INVENTORY_MODULE } from "../../../../../modules/hotel-management/room-inventory";
import RoomInventoryModuleService from "../../../../../modules/hotel-management/room-inventory/service";
import { CreateOrUpdateRoomInventoryWorkflow } from "../../../../../workflows/hotel-management/room-inventory/upsert-room-inventory";
import * as ExcelJS from "exceljs";
import multer from "multer";
import { z } from "zod";
import { parseISO } from "date-fns";

// Configure multer for file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 5MB limit
  },
  fileFilter: (req, file, cb) => {
    // Accept Excel and CSV files
    if (
      file.mimetype ===
        "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
      file.mimetype === "application/vnd.ms-excel" ||
      file.mimetype === "text/csv" ||
      file.mimetype === "application/csv"
    ) {
      cb(null, true);
    } else {
      cb(new Error("Only Excel (.xlsx, .xls) and CSV files are allowed"));
    }
  },
});

// Validation schema for room inventory data
const RoomInventorySchema = z
  .object({
    // Allow either inventory_item_id OR room_number + hotel_id
    inventory_item_id: z.string().optional(),
    room_number: z.string().optional(),
    hotel_id: z.string().optional(),
    from_date: z.union([
      z.string().min(1, "From date is required"),
      z.date(),
      z.number(), // Excel dates are numbers (days since 1900-01-01)
    ]),
    to_date: z.union([
      z.string().min(1, "To date is required"),
      z.date(),
      z.number(), // Excel dates are numbers (days since 1900-01-01)
    ]),
    available_quantity: z
      .union([
        z.number(),
        z.string().transform((val) => (val ? parseInt(val, 10) : 1)),
        z.null().transform(() => 1),
      ])
      .default(1),
    status: z
      .union([
        z.string().transform((val) => {
          // Transform user-friendly labels to internal status values
          const statusMapping = {
            Available: "available",
            Reserved: "reserved",
            Maintenance: "maintenance",
            Cleaning: "cleaning",
            Unavailable: "unavailable",
            "On Request": "on_demand",
            "On Hold": "on_hold",
          };

          // If it's already an internal status, keep it
          const internalStatuses = [
            "available",
            "reserved",
            "maintenance",
            "cleaning",
            "unavailable",
            "on_demand",
            "on_hold",
            "booked",
            "reserved_unassigned",
            "cart_reserved",
          ];
          if (internalStatuses.includes(val?.toLowerCase())) {
            return val.toLowerCase();
          }

          // Transform user-friendly label to internal status
          return statusMapping[val] || "available";
        }),
        z.null().transform(() => "available"),
      ])
      .optional()
      .default("available"),
    notes: z
      .union([
        z.string(),
        z.null().transform(() => ""),
        z.undefined().transform(() => ""),
      ])
      .optional(),
    check_in_time: z
      .union([
        z.string(),
        z.null().transform(() => "12:00"),
        z.undefined().transform(() => "12:00"),
      ])
      .optional()
      .default("12:00"),
    check_out_time: z
      .union([
        z.string(),
        z.null().transform(() => "12:00"),
        z.undefined().transform(() => "12:00"),
      ])
      .optional()
      .default("12:00"),
    is_noon_to_noon: z
      .union([
        z.boolean(),
        z.string().transform((val) => val?.toLowerCase() === "true"),
        z.null().transform(() => true),
        z.undefined().transform(() => true),
      ])
      .optional()
      .default(true),
  })
  .refine(
    (data) => {
      // Either inventory_item_id OR (room_number + hotel_id) must be provided
      return data.inventory_item_id || (data.room_number && data.hotel_id);
    },
    {
      message:
        "Either inventory_item_id OR both room_number and hotel_id must be provided",
    }
  );

/**
 * POST endpoint to import room inventory from Excel file
 */
export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  // Use multer to handle the file upload
  const multerUpload = upload.single("file");

  multerUpload(req, res, async (err) => {
    if (err) {
      return res.status(400).json({ message: err.message });
    }

    if (!req.file) {
      return res.status(400).json({ message: "No file uploaded" });
    }

    try {
      // Get services
      const hotelService: HotelModuleService = req.scope.resolve(HOTEL_MODULE);
      const roomInventoryService: RoomInventoryModuleService =
        req.scope.resolve(ROOM_INVENTORY_MODULE);

      // Get parameters from form data
      const hotelId = req.body.hotel_id;
      const overrideExisting = req.body.override_existing === "true";

      // Log the raw value and the parsed boolean to debug
      console.log(
        `Import options: override_existing=${overrideExisting} (raw value: '${
          req.body.override_existing
        }'), hotel_id=${hotelId || "not specified"}`
      );

      // Determine file type and parse accordingly
      const fileType = req.file.originalname.split(".").pop()?.toLowerCase();
      const workbook = new ExcelJS.Workbook();
      let worksheet;

      try {
        if (fileType === "csv") {
          // For CSV files, we need to convert the buffer to a string first
          const csvString = req.file.buffer.toString("utf8");

          // Create a temporary file to handle the CSV (ExcelJS CSV reader needs a file path)
          const tempFilePath = `/tmp/temp_${Date.now()}.csv`;
          require("fs").writeFileSync(tempFilePath, csvString);

          try {
            // Read the CSV file
            await workbook.csv.readFile(tempFilePath);
            worksheet = workbook.worksheets[0]; // CSV files have only one worksheet

            // Clean up the temporary file
            require("fs").unlinkSync(tempFilePath);
          } catch (csvError) {
            console.error("Error reading CSV:", csvError);

            // Alternative approach: parse CSV manually
            const rows = csvString.split("\\n").map((row) => row.split(","));

            if (rows.length > 0) {
              // Create a new worksheet
              worksheet = workbook.addWorksheet("Sheet1");

              // Add rows to the worksheet
              rows.forEach((row) => {
                worksheet.addRow(row);
              });
            } else {
              throw new Error("CSV file is empty or invalid");
            }
          }
        } else {
          // Parse Excel file
          await workbook.xlsx.load(req.file.buffer);

          // Try to find a worksheet named 'Room Inventory' or use the first one
          worksheet =
            workbook.getWorksheet("Room Inventory") ||
            workbook.getWorksheet("room inventory") ||
            workbook.getWorksheet("Sheet1") ||
            workbook.worksheets[0];
        }
      } catch (error) {
        console.error("Error parsing file:", error);
        return res
          .status(400)
          .json({ message: `Error parsing file: ${error.message}` });
      }

      if (!worksheet) {
        return res
          .status(400)
          .json({ message: "Invalid file: No worksheet found" });
      }

      // Get headers
      const headers = worksheet.getRow(1).values as string[];
      const headerMap = {};

      console.log("Raw headers:", headers);

      // Create a map of column index to header name
      // Note: ExcelJS uses 1-based indexing for columns
      for (let i = 1; i < headers.length; i++) {
        const header = headers[i];
        if (header) {
          const headerName = header.toString().trim().toLowerCase(); // Convert to lowercase for case-insensitive matching
          headerMap[i] = headerName;
          console.log(`Mapped column ${i} to header '${headerName}'`);
        }
      }

      console.log("Header map:", headerMap);

      // Prepare results
      const results = {
        total: 0,
        successful: 0,
        failed: 0,
        created: [],
        errors: [],
      };

      // Validate inventory item IDs (which are actually product variant IDs in room_inventory)
      // Get all product variants to validate against
      let productVariants = [];
      try {
        const query = req.scope.resolve("query");
        const { data: variants } = await query.graph({
          entity: "product_variant",
          fields: ["id", "metadata"],
        });

        if (variants && Array.isArray(variants)) {
          productVariants = variants;
          console.log(`Found ${productVariants.length} product variants`);
        } else {
          console.warn("No product variants found or variants is not an array");
        }
      } catch (error) {
        console.error("Error fetching product variants:", error);
      }

      // Create a set of valid inventory item IDs (which are actually variant IDs)
      const validInventoryItemIds = new Set(
        productVariants.map((variant) => variant.id)
      );

      // Process each row (skip header)
      for (let i = 2; i <= worksheet.rowCount; i++) {
        const row = worksheet.getRow(i);

        // Skip empty rows
        if (!row.hasValues) {
          continue;
        }

        results.total++;

        // Convert row to object using header map
        const rowData: Record<string, any> = {};
        row.eachCell({ includeEmpty: true }, (cell, colNumber) => {
          const header = headerMap[colNumber];
          if (header) {
            rowData[header] = cell.value;
          }
        });

        // Clean up the data before validation
        Object.keys(rowData).forEach((key) => {
          // Convert any objects to strings
          if (
            rowData[key] &&
            typeof rowData[key] === "object" &&
            !(rowData[key] instanceof Array)
          ) {
            rowData[key] = String(rowData[key]);
          }
        });

        // Debug log to see what data we're getting
        console.log("Row data:", rowData);

        // Map the lowercase header names back to the expected case-sensitive names
        const normalizedData: Record<string, any> = {};
        Object.keys(rowData).forEach((key) => {
          const lowerKey = key.toLowerCase();
          if (lowerKey === "inventory_item_id")
            normalizedData.inventory_item_id = rowData[key];
          else if (lowerKey === "from_date")
            normalizedData.from_date = rowData[key];
          else if (lowerKey === "to_date")
            normalizedData.to_date = rowData[key];
          else if (lowerKey === "available_quantity")
            normalizedData.available_quantity = rowData[key];
          else if (lowerKey === "status") normalizedData.status = rowData[key];
          else if (lowerKey === "notes") normalizedData.notes = rowData[key];
          else if (lowerKey === "check_in_time")
            normalizedData.check_in_time = rowData[key];
          else if (lowerKey === "check_out_time")
            normalizedData.check_out_time = rowData[key];
          else if (lowerKey === "is_noon_to_noon")
            normalizedData.is_noon_to_noon = rowData[key];
          else normalizedData[key] = rowData[key]; // Keep any other fields
        });

        console.log("Normalized data:", normalizedData);

        // Ensure required fields are present
        if (
          !normalizedData.inventory_item_id ||
          !normalizedData.from_date ||
          !normalizedData.to_date
        ) {
          results.failed++;
          results.errors.push({
            row: i,
            data: normalizedData,
            error: `Missing required fields: ${
              !normalizedData.inventory_item_id ? "inventory_item_id" : ""
            } ${!normalizedData.from_date ? "from_date" : ""} ${
              !normalizedData.to_date ? "to_date" : ""
            }`,
          });
          continue;
        }

        try {
          // Validate the data
          const validatedData = RoomInventorySchema.parse(normalizedData);

          // If room_number and hotel_id are provided instead of inventory_item_id, resolve it
          if (
            !validatedData.inventory_item_id &&
            validatedData.room_number &&
            validatedData.hotel_id
          ) {
            try {
              // Query to find the room variant ID based on room_number and hotel_id
              const query = req.scope.resolve("query");
              const { data: roomVariants } = await query.graph({
                entity: "product_variant",
                filters: {
                  "metadata.room_number": validatedData.room_number,
                  "product.metadata.hotel_id": validatedData.hotel_id,
                },
                fields: ["id", "metadata", "product.metadata"],
              });

              if (roomVariants && roomVariants.length > 0) {
                validatedData.inventory_item_id = roomVariants[0].id;
                console.log(
                  `Resolved room ${validatedData.room_number} in hotel ${validatedData.hotel_id} to inventory_item_id: ${validatedData.inventory_item_id}`
                );
              } else {
                throw new Error(
                  `Room ${validatedData.room_number} not found in hotel ${validatedData.hotel_id}. Please check the room number and hotel ID.`
                );
              }
            } catch (error) {
              throw new Error(
                `Failed to resolve room ${validatedData.room_number} in hotel ${validatedData.hotel_id}: ${error.message}`
              );
            }
          }

          // Validate inventory_item_id (now that it should be resolved)
          if (!validatedData.inventory_item_id) {
            throw new Error(
              "inventory_item_id is required. Please provide either inventory_item_id OR both room_number and hotel_id."
            );
          }

          if (
            validInventoryItemIds.size > 0 &&
            !validInventoryItemIds.has(validatedData.inventory_item_id)
          ) {
            throw new Error(
              `Invalid inventory_item_id: ${validatedData.inventory_item_id}. Please use a valid room variant ID or provide room_number + hotel_id instead.`
            );
          }

          // Parse dates
          let fromDate, toDate;
          try {
            console.log("Raw date values:", {
              from_date: validatedData.from_date,
              to_date: validatedData.to_date,
              from_date_type: typeof validatedData.from_date,
              to_date_type: typeof validatedData.to_date,
            });

            // DIRECT APPROACH: Try to manually parse the date strings
            // This is a more direct approach that should work regardless of the format
            const parseCustomDate = (dateStr) => {
              console.log(`Attempting to parse date: ${dateStr}`);

              // If it's already a Date object
              if (dateStr instanceof Date) {
                console.log("Date is already a Date object");
                // Return only the date part without time
                return new Date(dateStr.getFullYear(), dateStr.getMonth(), dateStr.getDate());
              }

              // If it's a number (Excel serial date)
              if (typeof dateStr === "number") {
                console.log("Date is a number (Excel serial date):", dateStr);
                // Excel serial date conversion - account for Excel's leap year bug
                // Excel incorrectly treats 1900 as a leap year, so we need to adjust
                const excelEpoch = new Date(1900, 0, 1); // Jan 1, 1900
                const adjustedDays = dateStr > 59 ? dateStr - 1 : dateStr; // Adjust for Excel's leap year bug
                const resultDate = new Date(
                  excelEpoch.getTime() +
                    (adjustedDays - 1) * 24 * 60 * 60 * 1000
                );
                console.log(
                  "Converted Excel date to:",
                  resultDate.toISOString()
                );
                // Return only the date part without time
                return new Date(resultDate.getFullYear(), resultDate.getMonth(), resultDate.getDate());
              }

              // If it's not a string at this point, we can't handle it
              if (typeof dateStr !== "string") {
                throw new Error(`Unsupported date type: ${typeof dateStr}`);
              }

              // Clean the string (remove any quotes, trim whitespace)
              dateStr = dateStr.replace(/["']/g, "").trim();
              console.log(`Cleaned date string: ${dateStr}`);

              // Try ISO format (YYYY-MM-DD)
              if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
                console.log(
                  "Date appears to be in ISO format (YYYY-MM-DD):",
                  dateStr
                );
                const [year, month, day] = dateStr.split("-").map(Number);
                // Create pure date without time component
                const resultDate = new Date(year, month - 1, day);
                console.log(
                  "Parsed ISO date to:",
                  resultDate.toISOString(),
                  "Local:",
                  resultDate.toLocaleDateString()
                );
                return resultDate;
              }

              // Try US format (MM/DD/YYYY)
              if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
                console.log(
                  "Date appears to be in US format (MM/DD/YYYY):",
                  dateStr
                );
                const [month, day, year] = dateStr.split("/").map(Number);
                // Create pure date without time component
                const resultDate = new Date(year, month - 1, day);
                console.log(
                  "Parsed US format date to:",
                  resultDate.toISOString()
                );
                return resultDate;
              }

              // Try formats with 2-digit year (DD/MM/YY or MM/DD/YY)
              if (/^\d{1,2}\/\d{1,2}\/\d{2}$/.test(dateStr)) {
                const [first, second, year] = dateStr.split("/").map(Number);
                // Convert 2-digit year to 4-digit (assume 20xx for years 00-99)
                const fullYear = year < 50 ? 2000 + year : 1900 + year;

                // Determine format based on values - if first > 12, it's likely DD/MM/YY
                if (first > 12) {
                  console.log(
                    "Date appears to be in European format with 2-digit year (DD/MM/YY):",
                    dateStr
                  );
                  // Create pure date without time component
                  const resultDate = new Date(fullYear, second - 1, first);
                  console.log(
                    "Parsed European 2-digit year date to:",
                    resultDate.toISOString()
                  );
                  return resultDate;
                } else if (second > 12) {
                  console.log(
                    "Date appears to be in US format with 2-digit year (MM/DD/YY):",
                    dateStr
                  );
                  // Create pure date without time component
                  const resultDate = new Date(fullYear, first - 1, second);
                  console.log(
                    "Parsed US 2-digit year date to:",
                    resultDate.toISOString()
                  );
                  return resultDate;
                } else {
                  // Ambiguous case - default to European format (DD/MM/YY) since that seems to be your format
                  console.log(
                    "Ambiguous date format, defaulting to European (DD/MM/YY):",
                    dateStr
                  );
                  // Create pure date in UTC to avoid timezone issues
                  const resultDate = new Date(
                    Date.UTC(fullYear, second - 1, first)
                  );
                  console.log(
                    "Parsed as European 2-digit year date to:",
                    resultDate.toISOString()
                  );
                  return resultDate;
                }
              }

              // Try European format (DD/MM/YYYY)
              if (/^\d{1,2}\/\d{1,2}\/\d{4}$/.test(dateStr)) {
                const [first, second, year] = dateStr.split("/").map(Number);

                // Determine format based on values - if first > 12, it's likely DD/MM/YYYY
                if (first > 12) {
                  console.log(
                    "Date appears to be in European format (DD/MM/YYYY):",
                    dateStr
                  );
                  // Create pure date in UTC to avoid timezone issues
                  const resultDate = new Date(
                    Date.UTC(year, second - 1, first)
                  );
                  console.log(
                    "Parsed European format date to:",
                    resultDate.toISOString()
                  );
                  return resultDate;
                } else if (second > 12) {
                  console.log(
                    "Date appears to be in US format (MM/DD/YYYY):",
                    dateStr
                  );
                  // Create pure date in UTC to avoid timezone issues
                  const resultDate = new Date(
                    Date.UTC(year, first - 1, second)
                  );
                  console.log(
                    "Parsed US format date to:",
                    resultDate.toISOString()
                  );
                  return resultDate;
                } else {
                  // Ambiguous case - default to European format since that seems to be your format
                  console.log(
                    "Ambiguous date format, defaulting to European (DD/MM/YYYY):",
                    dateStr
                  );
                  const resultDate = new Date(year, second - 1, first);
                  console.log(
                    "Parsed as European format date to:",
                    resultDate.toISOString()
                  );
                  return resultDate;
                }
              }

              // Try Excel's serial date as a string
              if (/^\d+(\.\d+)?$/.test(dateStr)) {
                console.log(
                  "Date appears to be an Excel serial date as a string:",
                  dateStr
                );
                const days = parseFloat(dateStr);
                // Excel serial date conversion - account for Excel's leap year bug
                const excelEpoch = new Date(1900, 0, 1); // Jan 1, 1900
                const adjustedDays = days > 59 ? days - 1 : days; // Adjust for Excel's leap year bug
                const resultDate = new Date(
                  excelEpoch.getTime() +
                    (adjustedDays - 1) * 24 * 60 * 60 * 1000
                );
                console.log(
                  "Converted Excel string date to:",
                  resultDate.toISOString()
                );
                // Return only the date part without time
                return new Date(resultDate.getFullYear(), resultDate.getMonth(), resultDate.getDate());
              }

              // Try to handle Excel date objects that have been converted to strings
              if (dateStr.includes("GMT") && dateStr.includes("Time")) {
                console.log(
                  "Date appears to be an Excel date object converted to string"
                );
                // Extract just the date part (ignore time)
                try {
                  const date = new Date(dateStr);
                  if (!isNaN(date.getTime())) {
                    // Return only the date part without time
                    return new Date(
                      date.getFullYear(),
                      date.getMonth(),
                      date.getDate()
                    );
                  }
                } catch (e) {
                  console.log("Failed to parse Excel date object string");
                }
              }

              // Try other common formats
              const formats = [
                "yyyy-MM-dd", // ISO
                "MM/dd/yyyy", // US
                "dd/MM/yyyy", // European
                "MMM d, yyyy", // Jan 1, 2023
                "MMMM d, yyyy", // January 1, 2023
                "d MMM yyyy", // 1 Jan 2023
                "d MMMM yyyy", // 1 January 2023
              ];

              // Try parseISO as a last resort
              try {
                console.log("Trying parseISO as a last resort");
                return parseISO(dateStr);
              } catch (e) {
                console.log("parseISO failed");
              }

              // If we get here, we couldn't parse the date
              throw new Error(`Could not parse date: ${dateStr}`);
            };

            // EMERGENCY FALLBACK: If we're dealing with Excel serial dates like 45764.22928240741
            // Let's try to convert them directly
            const convertExcelSerialDate = (serialDate) => {
              if (
                typeof serialDate === "number" ||
                (typeof serialDate === "string" &&
                  /^\d+(\.\d+)?$/.test(serialDate))
              ) {
                const days =
                  typeof serialDate === "number"
                    ? serialDate
                    : parseFloat(serialDate);
                console.log("Converting Excel serial date:", days);
                // Excel serial date conversion - account for Excel's leap year bug
                const excelEpoch = new Date(1900, 0, 1); // Jan 1, 1900
                const adjustedDays = days > 59 ? days - 1 : days; // Adjust for Excel's leap year bug
                const resultDate = new Date(
                  excelEpoch.getTime() +
                    (adjustedDays - 1) * 24 * 60 * 60 * 1000
                );
                console.log(
                  "Fallback converted Excel date to:",
                  resultDate.toISOString()
                );
                // Return only the date part without time
                return new Date(resultDate.getFullYear(), resultDate.getMonth(), resultDate.getDate());
              }
              return null;
            };

            // Parse the dates
            try {
              // First try our custom parser
              fromDate = parseCustomDate(validatedData.from_date);
              console.log(
                `Successfully parsed from_date: ${fromDate.toISOString()}`
              );
            } catch (e) {
              console.error(`Error parsing from_date: ${e.message}`);

              // Emergency fallback for Excel serial dates
              const fallbackDate = convertExcelSerialDate(
                validatedData.from_date
              );
              if (fallbackDate && !isNaN(fallbackDate.getTime())) {
                console.log(
                  `Using fallback method for from_date: ${fallbackDate.toISOString()}`
                );
                fromDate = fallbackDate;
              } else {
                // If all else fails, try to create a date directly
                try {
                  fromDate = new Date(validatedData.from_date as string);
                  if (isNaN(fromDate.getTime())) {
                    throw new Error(
                      `Could not parse from_date: ${validatedData.from_date}. Please use YYYY-MM-DD format.`
                    );
                  }
                  // Convert to UTC to avoid timezone issues
                  fromDate = new Date(
                    Date.UTC(
                      fromDate.getFullYear(),
                      fromDate.getMonth(),
                      fromDate.getDate()
                    )
                  );
                  console.log(
                    `Created date directly for from_date: ${fromDate.toISOString()}`
                  );
                } catch (directError) {
                  throw new Error(
                    `Could not parse from_date: ${validatedData.from_date}. Please use YYYY-MM-DD format.`
                  );
                }
              }
            }

            try {
              // First try our custom parser
              toDate = parseCustomDate(validatedData.to_date);
              console.log(
                `Successfully parsed to_date: ${toDate.toISOString()}`
              );
            } catch (e) {
              console.error(`Error parsing to_date: ${e.message}`);

              // Emergency fallback for Excel serial dates
              const fallbackDate = convertExcelSerialDate(
                validatedData.to_date
              );
              if (fallbackDate && !isNaN(fallbackDate.getTime())) {
                console.log(
                  `Using fallback method for to_date: ${fallbackDate.toISOString()}`
                );
                toDate = fallbackDate;
              } else {
                // If all else fails, try to create a date directly
                try {
                  toDate = new Date(validatedData.to_date as string);
                  if (isNaN(toDate.getTime())) {
                    throw new Error(
                      `Could not parse to_date: ${validatedData.to_date}. Please use YYYY-MM-DD format.`
                    );
                  }
                  // Convert to UTC to avoid timezone issues
                  toDate = new Date(
                    Date.UTC(
                      toDate.getFullYear(),
                      toDate.getMonth(),
                      toDate.getDate()
                    )
                  );
                  console.log(
                    `Created date directly for to_date: ${toDate.toISOString()}`
                  );
                } catch (directError) {
                  throw new Error(
                    `Could not parse to_date: ${validatedData.to_date}. Please use YYYY-MM-DD format.`
                  );
                }
              }
            }

            // Check if dates are valid
            if (isNaN(fromDate.getTime()) || isNaN(toDate.getTime())) {
              console.error("Invalid date values:", {
                fromDate,
                toDate,
                from_date: validatedData.from_date,
                to_date: validatedData.to_date,
              });
              throw new Error("Invalid date format");
            }

            console.log("Parsed dates:", {
              fromDate: fromDate.toISOString(),
              toDate: toDate.toISOString(),
              originalFromDate: validatedData.from_date,
              originalToDate: validatedData.to_date,
            });
          } catch (error) {
            console.error("Date parsing error:", error, {
              from_date: validatedData.from_date,
              to_date: validatedData.to_date,
            });
            throw new Error(
              `Invalid date format. Please use YYYY-MM-DD format for dates. Error: ${error.message}`
            );
          }

          // Ensure to_date is after from_date
          if (fromDate >= toDate) {
            throw new Error(`To date must be after from date.`);
          }

          // If not overriding existing records, check if a record already exists for this date range
          if (!overrideExisting) {
            console.log(
              `Override is disabled. Checking for existing records for item ${
                validatedData.inventory_item_id
              } from ${fromDate.toISOString()} to ${toDate.toISOString()}`
            );
            try {
              // Check if inventory records already exist for this date range and inventory item
              // We need to query the database directly to check for overlapping date ranges
              const query = req.scope.resolve("query");
              const { data: existingInventory } = await query.graph({
                entity: "room_inventory",
                filters: {
                  inventory_item_id: validatedData.inventory_item_id,
                  // Check for any overlapping date ranges
                  // This is a simplified check - in a real system you'd use a more complex query
                  // to check for all possible overlapping scenarios
                  $or: [
                    // Case 1: fromDate is between existing from_date and to_date
                    {
                      from_date: { $lte: fromDate },
                      to_date: { $gte: fromDate },
                    },
                    // Case 2: toDate is between existing from_date and to_date
                    { from_date: { $lte: toDate }, to_date: { $gte: toDate } },
                    // Case 3: existing range is completely inside new range
                    {
                      from_date: { $gte: fromDate },
                      to_date: { $lte: toDate },
                    },
                  ],
                },
                fields: ["id", "from_date", "to_date"],
              });

              if (existingInventory && existingInventory.length > 0) {
                console.log(
                  `Found ${existingInventory.length} existing inventory records that overlap with the date range. Skipping since override is disabled.`
                );
                throw new Error(
                  `Record already exists for this date range and will not be updated (override option is disabled).`
                );
              } else {
                console.log(
                  `No existing inventory records found for this date range. Proceeding with creation.`
                );
              }
            } catch (error) {
              if (
                error.message &&
                error.message.includes("will not be updated")
              ) {
                throw error; // Re-throw our custom error
              }
              // If the error is not our custom error, it's likely a service error, so we'll continue
              console.warn(
                `Error checking for existing inventory: ${
                  error.message || error
                }`
              );
              console.log(
                "Continuing with creation despite error checking for existing records"
              );
            }
          } else {
            console.log(
              `Override is enabled. Will update any existing records for item ${
                validatedData.inventory_item_id
              } from ${fromDate.toISOString()} to ${toDate.toISOString()}`
            );
          }

          // Create or update the room inventory using the workflow
          console.log("🔄 About to create/update room inventory with data:", {
            inventory_item_id: validatedData.inventory_item_id,
            from_date: fromDate.toISOString(),
            to_date: toDate.toISOString(),
            available_quantity: validatedData.available_quantity,
            status: validatedData.status,
            notes: validatedData.notes,
            check_in_time: validatedData.check_in_time,
            check_out_time: validatedData.check_out_time,
            is_noon_to_noon: validatedData.is_noon_to_noon,
          });

          const { result } = await CreateOrUpdateRoomInventoryWorkflow(
            req.scope
          ).run({
            input: {
              inventory_item_id: validatedData.inventory_item_id as string,
              from_date: fromDate,
              to_date: toDate,
              available_quantity: validatedData.available_quantity as number,
              status: validatedData.status as string,
              notes: validatedData.notes as string,
              check_in_time: validatedData.check_in_time as string,
              check_out_time: validatedData.check_out_time as string,
              is_noon_to_noon: validatedData.is_noon_to_noon as boolean,
            },
          });

          // Format the result for console display with date-only format
          const formattedResult = result.map(item => ({
            ...item,
            from_date: item.from_date ? item.from_date.split('T')[0] : item.from_date,
            to_date: item.to_date ? item.to_date.split('T')[0] : item.to_date,
          }));

          console.log("✅ Workflow completed with result:", formattedResult);

          results.successful++;
          results.created.push({
            inventory_item_id: validatedData.inventory_item_id,
            from_date: fromDate,
            to_date: toDate,
            row: i,
          });
        } catch (error) {
          results.failed++;
          results.errors.push({
            row: i,
            data: normalizedData,
            error: error.message || "Unknown error",
          });
        }
      }

      // Return the results
      return res.status(200).json({
        message: "Import completed",
        results,
      });
    } catch (error) {
      console.error("Error importing room inventory:", error);
      return res
        .status(500)
        .json({ message: `Error importing room inventory: ${error.message}` });
    }
  });
};
