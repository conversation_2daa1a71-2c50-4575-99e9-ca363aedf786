import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { FileText } from "lucide-react";
import { Container, Heading, Text } from "@camped-ai/ui";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { RoleGuard } from "../../../components/rbac/RoleGuard";
import { lazy, Suspense } from "react";

// Dynamically import page client for better performance
const SupplierRequestsPageClient = lazy(() => import("./page-client"));

const SupplierRequestsPage = () => {
  return (
    <>
      <PermissionBasedSidebarHider />
      <RoleGuard
        requirePermission="supplier_orders:view"
        fallback={
          <Container>
            <div className="p-8 text-center">
              <Heading level="h1">Access Denied</Heading>
              <Text className="mt-2">
                You don't have permission to view supplier order management (including on-request items).
              </Text>
            </div>
          </Container>
        }
      >
        <Suspense
          fallback={
            <Container className="p-6">
              <div className="flex items-center justify-center py-12">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                <span className="ml-3 text-sm text-gray-600">Loading requests...</span>
              </div>
            </Container>
          }
        >
          <SupplierRequestsPageClient />
        </Suspense>
      </RoleGuard>
    </>
  );
};

export const config = defineRouteConfig({
  label: "On Requests",
  icon: FileText,
});

export default SupplierRequestsPage;
