import React from "react";
import { Container, <PERSON><PERSON>, <PERSON>, Badge, But<PERSON> } from "@camped-ai/ui";
import { 
  Settings, 
  Calendar, 
  User, 
  Eye, 
  <PERSON>Off, 
  Trash2,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON> as LinkIcon,
  Star,
  Shield,
  DollarSign
} from "lucide-react";
import { HotelData } from "../../../../../types";
import Prompt from "../../../../../components/prompt";

interface HotelSettingsTabProps {
  hotel: HotelData | null;
  onDelete: () => void;
  deleteOpen: boolean;
  setDeleteOpen: (open: boolean) => void;
  hasDeletePermission: boolean;
  hasEditPermission: boolean;
}

const HotelSettingsTab: React.FC<HotelSettingsTabProps> = ({
  hotel,
  onDelete,
  deleteOpen,
  setDeleteOpen,
  hasDeletePermission,
  hasEditPermission,
}) => {
  if (!hotel) {
    return (
      <Container className="p-6">
        <Text className="text-muted-foreground">Loading hotel settings...</Text>
      </Container>
    );
  }

  const formatDate = (dateString: string | undefined) => {
    if (!dateString) return "Not available";
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  return (
    <div className="space-y-6">
      {/* Basic Settings */}
      <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border border-border">
        <div className="px-6 py-4 border-b border-border flex items-center gap-3 bg-muted/50">
          <Settings className="w-5 h-5 text-muted-foreground flex-shrink-0" />
          <Heading
            level="h2"
            className="text-lg font-medium text-foreground"
          >
            Hotel Settings
          </Heading>
        </div>
        <div className="p-6 space-y-6">
          {/* Handle */}
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <LinkIcon className="w-4 h-4 text-muted-foreground" />
              <Text className="text-sm font-medium text-foreground">Handle (URL Slug)</Text>
            </div>
            <div className="p-3 bg-muted/50 rounded-lg border border-border">
              <Text className="text-sm font-mono text-foreground">{hotel.handle}</Text>
            </div>
          </div>

          {/* Visibility Settings */}
          <div className="space-y-3">
            <Text className="text-sm font-medium text-foreground">Visibility</Text>
            <div className="flex flex-wrap gap-3">
              <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
                {hotel.is_active ? (
                  <Eye className="w-4 h-4 text-green-600" />
                ) : (
                  <EyeOff className="w-4 h-4 text-red-600" />
                )}
                <div>
                  <Text className="text-sm font-medium text-foreground">
                    {hotel.is_active ? "Active" : "Inactive"}
                  </Text>
                  <Text className="text-xs text-muted-foreground">
                    {hotel.is_active 
                      ? "Visible to users and booking systems"
                      : "Hidden from public view"
                    }
                  </Text>
                </div>
              </div>

              <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
                <Star className={`w-4 h-4 ${hotel.is_featured ? 'text-yellow-600' : 'text-gray-400'}`} />
                <div>
                  <Text className="text-sm font-medium text-foreground">
                    {hotel.is_featured ? "Featured" : "Not Featured"}
                  </Text>
                  <Text className="text-xs text-muted-foreground">
                    {hotel.is_featured 
                      ? "Highlighted in search results"
                      : "Standard listing priority"
                    }
                  </Text>
                </div>
              </div>

              <div className="flex items-center gap-2 p-3 bg-muted/50 rounded-lg">
                <Shield className={`w-4 h-4 ${hotel.is_pets_allowed ? 'text-green-600' : 'text-gray-400'}`} />
                <div>
                  <Text className="text-sm font-medium text-foreground">
                    {hotel.is_pets_allowed ? "Pet Friendly" : "No Pets"}
                  </Text>
                  <Text className="text-xs text-muted-foreground">
                    {hotel.is_pets_allowed 
                      ? "Pets are welcome"
                      : "Pets not allowed"
                    }
                  </Text>
                </div>
              </div>
            </div>
          </div>

          {/* Hotel Details */}
          <div className="space-y-3">
            <Text className="text-sm font-medium text-foreground">Hotel Information</Text>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {hotel.rating && (
                <div className="space-y-1">
                  <Text className="text-xs text-muted-foreground">Rating</Text>
                  <div className="flex items-center gap-2">
                    <Star className="w-4 h-4 text-yellow-500 fill-current" />
                    <Text className="text-sm font-medium">{hotel.rating} stars</Text>
                  </div>
                </div>
              )}

              {hotel.currency && (
                <div className="space-y-1">
                  <Text className="text-xs text-muted-foreground">Currency</Text>
                  <div className="flex items-center gap-2">
                    <DollarSign className="w-4 h-4 text-green-600" />
                    <Text className="text-sm font-medium">{hotel.currency}</Text>
                  </div>
                </div>
              )}

              {hotel.timezone && (
                <div className="space-y-1">
                  <Text className="text-xs text-muted-foreground">Timezone</Text>
                  <Text className="text-sm font-medium">{hotel.timezone}</Text>
                </div>
              )}

              {hotel.total_reviews && (
                <div className="space-y-1">
                  <Text className="text-xs text-muted-foreground">Total Reviews</Text>
                  <Text className="text-sm font-medium">{hotel.total_reviews} reviews</Text>
                </div>
              )}
            </div>
          </div>
        </div>
      </Container>

      {/* Metadata */}
      <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border border-border">
        <div className="px-6 py-4 border-b border-border flex items-center gap-3 bg-muted/50">
          <Calendar className="w-5 h-5 text-muted-foreground flex-shrink-0" />
          <Heading
            level="h2"
            className="text-lg font-medium text-foreground"
          >
            Metadata
          </Heading>
        </div>
        <div className="p-6 space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-1">
              <Text className="text-xs text-muted-foreground">Created</Text>
              <Text className="text-sm font-medium text-foreground">
                {formatDate(hotel.created_at)}
              </Text>
            </div>
            <div className="space-y-1">
              <Text className="text-xs text-muted-foreground">Last Updated</Text>
              <Text className="text-sm font-medium text-foreground">
                {formatDate(hotel.updated_at)}
              </Text>
            </div>
          </div>
          
          <div className="space-y-1">
            <Text className="text-xs text-muted-foreground">Hotel ID</Text>
            <div className="p-2 bg-muted/50 rounded border border-border">
              <Text className="text-sm font-mono text-foreground">{hotel.id}</Text>
            </div>
          </div>
        </div>
      </Container>

      {/* Danger Zone */}
      {/*hasDeletePermission && (
        <Container className="shadow-sm hover:shadow-md transition-shadow duration-200 p-0 rounded-lg overflow-hidden bg-card border-red-200 dark:border-red-800 border border-border">
          <div className="px-6 py-4 border-b border-red-200 dark:border-red-800 flex items-center gap-3 bg-red-50 dark:bg-red-950/30">
            <AlertTriangle className="w-5 h-5 text-red-600 dark:text-red-400 flex-shrink-0" />
            <Heading
              level="h2"
              className="text-lg font-medium text-red-800 dark:text-red-200"
            >
              Danger Zone
            </Heading>
          </div>
          <div className="p-6">
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div>
                <Text className="font-medium text-foreground mb-1">
                  Delete Hotel
                </Text>
                <Text className="text-sm text-muted-foreground">
                  Permanently delete this hotel and all associated data. This action cannot be undone.
                </Text>
              </div>
              <Prompt
                open={deleteOpen}
                onOpenChange={setDeleteOpen}
                title="Delete Hotel"
                description="Are you sure you want to delete this hotel? This action cannot be undone and will remove all associated rooms, bookings, and data."
                onDelete={onDelete}
                trigger={
                  <Button
                    variant="danger"
                    size="small"
                    className="flex items-center gap-2"
                  >
                    <Trash2 className="w-4 h-4" />
                    Delete Hotel
                  </Button>
                }
              />
            </div>
          </div>
        </Container>
      )*/}
    </div>
  );
};

export default HotelSettingsTab;
