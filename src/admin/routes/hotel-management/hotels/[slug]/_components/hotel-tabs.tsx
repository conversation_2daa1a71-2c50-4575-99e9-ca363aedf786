import React from "react";
import { Container, Tabs } from "@camped-ai/ui";
import {
  FileText,
  Shield,
  BarChart3,
  Settings,
  Sparkles
} from "lucide-react";

export type HotelTabId = "overview" | "facilities" | "insights" | "bailey-ai" | "settings";

export interface HotelTab {
  id: HotelTabId;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  count?: number;
}

interface HotelTabsProps {
  activeTab: HotelTabId;
  onTabChange: (tabId: HotelTabId) => void;
  tabs?: HotelTab[];
}

const defaultTabs: HotelTab[] = [
  {
    id: "overview",
    label: "Overview",
    icon: FileText,
  },
  {
    id: "facilities",
    label: "Facilities & Policies",
    icon: Shield,
  },
  {
    id: "insights",
    label: "Insights",
    icon: BarChart3,
  },
  {
    id: "bailey-ai",
    label: "Bailey AI",
    icon: Sparkles,
  },
  {
    id: "settings",
    label: "Settings",
    icon: Settings,
  },
];

const HotelTabs: React.FC<HotelTabsProps> = ({
  activeTab,
  onTabChange,
  tabs = defaultTabs,
}) => {
  return (
    <Tabs value={activeTab} onValueChange={(value) => onTabChange(value as HotelTabId)}>
      <Container>
        <Tabs.List className={`grid flex w-full grid-cols-${tabs.length}`}>
          {tabs.map((tab) => {
            const Icon = tab.icon;
            return (
              <Tabs.Trigger key={tab.id} value={tab.id} className="flex items-center gap-2">
                <Icon className="h-4 w-4" />
                {tab.label}
              </Tabs.Trigger>
            );
          })}
        </Tabs.List>
      </Container>
    </Tabs>
  );
};

export default HotelTabs;
