import React, { useState, useMemo } from "react";
import {
  Container,
  Heading,
  Text,
  Select,
} from "@camped-ai/ui";
import {
  Calendar,
} from "lucide-react";
import { useAdminCurrencies } from "../../../hooks/use-admin-currencies";
import { CurrencySelector } from "../../common/currency-selector";
import { format } from "date-fns";
import HotelPricingTableSkeleton from "../../shared/hotel-pricing-table-skeleton";

type RoomConfig = {
  id: string;
  title: string;
  handle?: string;
  description?: string;
};

type OccupancyConfig = {
  id: string;
  name: string;
};

type MealPlan = {
  id: string;
  name: string;
};

type SeasonalPeriod = {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
};

type PricingRow = {
  id: string;
  roomConfigId: string;
  occupancyTypeId: string;
  mealPlanId: string | null;
  seasonalPeriodId?: string;
  prices: {
    mon: number;
    tue: number;
    wed: number;
    thu: number;
    fri: number;
    sat: number;
    sun: number;
  };
};

type ReadOnlyPricingTableProps = {
  hotelId: string;
  roomConfigs: RoomConfig[];
  occupancyConfigs: OccupancyConfig[];
  mealPlans: MealPlan[];
  seasonalPeriods: SeasonalPeriod[];
  pricingRows: PricingRow[];
  currencyCode: string;
  isLoading?: boolean;
};

const ReadOnlyPricingTable: React.FC<ReadOnlyPricingTableProps> = ({
  roomConfigs,
  occupancyConfigs,
  mealPlans,
  seasonalPeriods,
  pricingRows,
  currencyCode,
  isLoading = false,
}) => {
  const { currencies } = useAdminCurrencies();

  // Filter states
  const [selectedSeason, setSelectedSeason] = useState<string>("base");
  const [selectedRoomType, setSelectedRoomType] = useState<string>("all");
  const [selectedOccupancyType, setSelectedOccupancyType] = useState<string>("all");
  const [selectedMealPlan, setSelectedMealPlan] = useState<string>("all");

  const weekdays = [
    { id: "mon", name: "Monday" },
    { id: "tue", name: "Tuesday" },
    { id: "wed", name: "Wednesday" },
    { id: "thu", name: "Thursday" },
    { id: "fri", name: "Friday" },
    { id: "sat", name: "Saturday" },
    { id: "sun", name: "Sunday" },
  ];

  const getCurrencySymbol = () => {
    const currency = currencies.find((c: any) => c.code === currencyCode);
    return currency?.symbol || currencyCode;
  };

  const formatPrice = (price: number): string => {
    return Number(price).toFixed(2);
  };

  // Filter the pricing rows based on selected filters
  const filteredPricingRows = useMemo(() => {
    return pricingRows.filter((row) => {
      // Season filter
      if (selectedSeason !== "all") {
        if (selectedSeason === "base" && row.seasonalPeriodId) return false;
        if (selectedSeason !== "base" && row.seasonalPeriodId !== selectedSeason) return false;
      }

      // Room type filter
      if (selectedRoomType !== "all" && row.roomConfigId !== selectedRoomType) return false;

      // Occupancy type filter
      if (selectedOccupancyType !== "all" && row.occupancyTypeId !== selectedOccupancyType) return false;

      // Meal plan filter
      if (selectedMealPlan !== "all") {
        if (selectedMealPlan === "none" && row.mealPlanId !== null) return false;
        if (selectedMealPlan !== "none" && row.mealPlanId !== selectedMealPlan) return false;
      }

      return true;
    });
  }, [pricingRows, selectedSeason, selectedRoomType, selectedOccupancyType, selectedMealPlan]);

  if (isLoading) {
    return <HotelPricingTableSkeleton />;
  }

  return (
    <div>
      {/* Header */}
      <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-6">

        <div className="flex flex-wrap items-center gap-2">
          {/* {currencies.length > 1 && (
            <CurrencySelector
              value={currencyCode}
              onChange={() => {}} // Read-only, no changes allowed
              label="Currency"
              id="currency"
              disabled={true}
            />
          )} */}
        </div>
      </div>

      {/* Filters */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4 bg-muted/30 rounded-lg">
        {/* Season Filter */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Season
          </label>
          <Select
            value={selectedSeason}
            onValueChange={setSelectedSeason}
          >
            <Select.Trigger className="w-full">
              <Select.Value placeholder="Base Pricing" />
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="base">Base Pricing</Select.Item>
              {seasonalPeriods.map((period) => (
                <Select.Item key={period.id} value={period.id}>
                  {period.name}
                </Select.Item>
              ))}
            </Select.Content>
          </Select>
        </div>

        {/* Room Type Filter */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Room Type
          </label>
          <Select
            value={selectedRoomType}
            onValueChange={setSelectedRoomType}
          >
            <Select.Trigger className="w-full">
              <Select.Value placeholder="All Room Types" />
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="all">All Room Types</Select.Item>
              {roomConfigs.map((room) => (
                <Select.Item key={room.id} value={room.id}>
                  {room.title}
                </Select.Item>
              ))}
            </Select.Content>
          </Select>
        </div>

        {/* Occupancy Type Filter */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Occupancy Type
          </label>
          <Select
            value={selectedOccupancyType}
            onValueChange={setSelectedOccupancyType}
          >
            <Select.Trigger className="w-full">
              <Select.Value placeholder="All Occupancy Types" />
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="all">All Occupancy Types</Select.Item>
              {occupancyConfigs.map((occupancy) => (
                <Select.Item key={occupancy.id} value={occupancy.id}>
                  {occupancy.name}
                </Select.Item>
              ))}
            </Select.Content>
          </Select>
        </div>

        {/* Meal Plan Filter */}
        <div>
          <label className="block text-sm font-medium text-foreground mb-2">
            Meal Plan
          </label>
          <Select
            value={selectedMealPlan}
            onValueChange={setSelectedMealPlan}
          >
            <Select.Trigger className="w-full">
              <Select.Value placeholder="All Meal Plans" />
            </Select.Trigger>
            <Select.Content>
              <Select.Item value="all">All Meal Plans</Select.Item>
              <Select.Item value="none">No Meal Plan</Select.Item>
              {mealPlans.map((mealPlan) => (
                <Select.Item key={mealPlan.id} value={mealPlan.id}>
                  {mealPlan.name}
                </Select.Item>
              ))}
            </Select.Content>
          </Select>
        </div>
      </div>

      <div className="overflow-x-auto rounded-lg border border-border shadow-sm">
        <table className="min-w-full divide-y divide-border table-fixed">
          <thead className="bg-muted/50">
            <tr>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-32">
                Season
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-32">
                Room Type
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-20">
                Occupancy
              </th>
              <th className="px-3 py-2 text-left text-xs font-medium text-muted-foreground uppercase tracking-wide w-28">
                Meal Plan
              </th>

              {/* Weekday Price Columns */}
              {weekdays.map((day, index) => (
                <th
                  key={day.id}
                  className={`px-3 py-2 text-center text-xs font-medium text-muted-foreground uppercase tracking-wide ${
                    index === 0
                      ? "border-l-2 border-l-blue-400"
                      : "border-l-2 border-l-blue-300"
                  } ${
                    index === weekdays.length - 1
                      ? "border-r-2 border-r-blue-400"
                      : ""
                  } bg-blue-50/50 dark:bg-blue-950/20`}
                >
                  <div className="flex flex-col items-center gap-0.5">
                    <span className="font-semibold text-blue-600 dark:text-blue-400">
                      {day.name}
                    </span>
                    <span className="text-xs text-muted-foreground/70 font-normal">
                      {getCurrencySymbol()}
                    </span>
                  </div>
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-background divide-y divide-border">
            {filteredPricingRows.map((row, index) => {
              // Find the room, occupancy, and meal plan objects
              const room = roomConfigs.find((r) => r.id === row.roomConfigId);
              const occupancy = occupancyConfigs.find(
                (o) => o.id === row.occupancyTypeId
              );
              const mealPlan = mealPlans.find((m) => m.id === row.mealPlanId);
              const seasonalPeriod = row.seasonalPeriodId
                ? seasonalPeriods.find((s) => s.id === row.seasonalPeriodId)
                : undefined;

              return (
                <tr
                  key={row.id}
                  className={index % 2 === 0 ? "bg-background" : "bg-muted/50"}
                >
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-32">
                    {seasonalPeriod ? (
                      <div className="flex items-center gap-1">
                        <Calendar className="w-3 h-3 text-blue-600 dark:text-blue-400 flex-shrink-0" />
                        <div className="flex flex-col min-w-0">
                          <span
                            className="font-medium truncate text-xs"
                            title={seasonalPeriod.name}
                          >
                            {seasonalPeriod.name}
                          </span>
                          <span className="text-xs text-muted-foreground truncate">
                            {format(
                              new Date(seasonalPeriod.start_date),
                              "MMM d"
                            )}{" "}
                            -{" "}
                            {format(new Date(seasonalPeriod.end_date), "MMM d")}
                          </span>
                        </div>
                      </div>
                    ) : (
                      <span className="font-medium text-foreground text-xs">
                        Base Price
                      </span>
                    )}
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-32">
                    <div
                      className="truncate text-xs"
                      title={room?.title || "Unknown Room"}
                    >
                      <span className="font-medium">
                        {room?.title || "Unknown Room"}
                      </span>
                    </div>
                  </td>
                  <td className="px-2 py-3 whitespace-nowrap text-sm w-20">
                    <div
                      className="truncate text-xs"
                      title={occupancy?.name || "Unknown Occupancy"}
                    >
                      {occupancy?.name || "Unknown Occupancy"}
                    </div>
                  </td>
                  <td className="px-3 py-3 whitespace-nowrap text-sm w-28">
                    <div
                      className="truncate"
                      title={
                        (occupancy as any)?.type === "EXTRA_BED" ||
                        occupancy?.name?.toLowerCase().includes("extra bed") ||
                        (occupancy as any)?.type === "COT" ||
                        occupancy?.name?.toLowerCase().includes("cot")
                          ? "-"
                          : mealPlan?.name || "Unknown Meal Plan"
                      }
                    >
                      {(occupancy as any)?.type === "EXTRA_BED" ||
                      occupancy?.name?.toLowerCase().includes("extra bed") ||
                      (occupancy as any)?.type === "COT" ||
                      occupancy?.name?.toLowerCase().includes("cot") ? (
                        <span className="text-muted-foreground">-</span>
                      ) : (
                        mealPlan?.name || "Unknown Meal Plan"
                      )}
                    </div>
                  </td>

                  {/* Weekday Price Columns - Read-only display */}
                  {weekdays.map((day, dayIndex) => (
                    <td
                      key={day.id}
                      className={`px-3 py-3 whitespace-nowrap text-center ${
                        dayIndex === 0
                          ? "border-l-2 border-l-blue-400"
                          : "border-l-2 border-l-blue-300"
                      } ${
                        dayIndex === weekdays.length - 1
                          ? "border-r-2 border-r-blue-400"
                          : ""
                      } bg-blue-50/30 dark:bg-blue-950/10`}
                    >
                      <div className="text-sm font-medium text-foreground">
                        {formatPrice(
                          row.prices[day.id as keyof typeof row.prices]
                        )}
                      </div>
                    </td>
                  ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default ReadOnlyPricingTable;
