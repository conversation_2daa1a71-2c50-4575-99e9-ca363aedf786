import React, { useEffect } from "react";
import { use<PERSON><PERSON>, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  Button,
  Input,
  Textarea,
  Select,
  Label,
  FocusModal,
  Heading,
  Text,
  toast,
} from "@camped-ai/ui";
import { useTranslation } from "react-i18next";
import {
  useCreateConciergeTask,
  useCreateBookingTask,
  useUpdateConciergeTask,
  ConciergeTask,
} from "../../hooks/api/concierge-tasks";
import { UserSelector } from "./user-selector";
import "../../styles/task-modal.css";

// Form validation schema
const taskFormSchema = z.object({
  title: z.string()
    .min(1, "Title is required")
    .max(255, "Title must be less than 255 characters")
    .trim(),
  description: z.string()
    .max(1000, "Description must be less than 1000 characters")
    .optional()
    .or(z.literal("")),
  status: z.enum(["pending", "in_progress", "review", "completed", "cancelled"], {
    errorMap: () => ({ message: "Please select a valid status" })
  }).default("pending"),
  priority: z.enum(["low", "medium", "high", "urgent"], {
    errorMap: () => ({ message: "Please select a valid priority" })
  }).default("medium"),
  entity_type: z.string().optional().or(z.literal("")).or(z.literal("general")),
  entity_id: z.string().optional().or(z.literal("")),
  assigned_to: z.string()
    .max(100, "Assigned to field must be less than 100 characters")
    .optional()
    .or(z.literal("")),
  due_date: z.string()
    .optional()
    .or(z.literal(""))
    .refine((date) => {
      if (!date) return true; // Optional field
      const parsedDate = new Date(date);
      return !isNaN(parsedDate.getTime());
    }, "Please enter a valid date"),
});

type TaskFormData = z.infer<typeof taskFormSchema>;

interface TaskFormModalProps {
  isOpen?: boolean;
  onClose: () => void;
  task?: ConciergeTask; // For editing existing tasks
  bookingId?: string; // For creating tasks linked to a booking
  entityType?: string; // For creating tasks linked to other entities
  entityId?: string;
  bookingDisplayId?: string; // For showing booking context
  inline?: boolean; // If true, render as inline form instead of modal
  defaultAssignedTo?: string; // Default value for assigned_to when creating
}

export const TaskFormModal: React.FC<TaskFormModalProps> = ({
  isOpen,
  onClose,
  task,
  bookingId,
  entityType,
  entityId,
  bookingDisplayId,
  inline = false,
  defaultAssignedTo = "",
}) => {
  const { t } = useTranslation();
  const isEditing = !!task;
  const isBookingContext = !!bookingId;

  // Choose the appropriate mutation based on context
  const createTaskMutation = useCreateConciergeTask();
  const createBookingTaskMutation = useCreateBookingTask(bookingId || "");
  const updateTaskMutation = useUpdateConciergeTask();

  const {
    register,
    handleSubmit,
    reset,
    setValue,
    watch,
    control,
    formState: { errors, isSubmitting },
  } = useForm<TaskFormData>({
    resolver: zodResolver(taskFormSchema),
    defaultValues: {
      title: "",
      description: "",
      status: "pending",
      priority: "medium",
      entity_type: "general",
      entity_id: "",
      assigned_to: "",
      due_date: "",
    },
  });

  // Initialize form when modal opens
  useEffect(() => {
    if (isOpen || inline) {
      if (isEditing && task) {
        // Populate form fields when editing a task
        setValue("title", task.title || "");
        setValue("description", task.description || "");
        setValue("status", task.status || "pending");
        setValue("priority", task.priority || "medium");
        setValue("entity_type", task.entity_type || "general");
        setValue("entity_id", task.entity_id || "");
        setValue("assigned_to", task.assigned_to || "");
        setValue("due_date", task.due_date ? new Date(task.due_date).toISOString().split('T')[0] : "");
      } else {
        // Reset to default values for new task
        reset({
          title: "",
          description: "",
          status: "pending",
          priority: "medium",
          entity_type: isBookingContext ? "booking" : entityType || "general",
          entity_id: isBookingContext ? bookingId || "" : entityId || "",
          assigned_to: defaultAssignedTo || "",
          due_date: "",
        });
      }
    }
  }, [isOpen, inline, isEditing, task, isBookingContext, bookingId, entityType, entityId, setValue, reset, defaultAssignedTo]);

  const onSubmit = async (data: TaskFormData) => {
    try {
      const taskData = {
        ...data,
        due_date: data.due_date ? new Date(data.due_date).toISOString() : undefined,
        // Ensure entity fields are set for booking context
        entity_type: isBookingContext && !isEditing ? "booking" : (data.entity_type === "general" ? "" : data.entity_type),
        entity_id: isBookingContext && !isEditing ? bookingId : data.entity_id,
      };

      if (isEditing && task) {
        // Update existing task
        await updateTaskMutation.mutateAsync({
          taskId: task.id,
          data: taskData,
        });
        // Success toast is handled by the mutation hook
      } else if (bookingId) {
        // Create task for booking
        await createBookingTaskMutation.mutateAsync(taskData);
        // Success toast is handled by the mutation hook
      } else {
        // Create general task
        await createTaskMutation.mutateAsync(taskData);
        // Success toast is handled by the mutation hook
      }

      onClose();
    } catch (error) {
      console.error("Error saving task:", error);
      toast.error("Failed to save task. Please try again.");
    }
  };

  const handleClose = () => {
    reset();
    onClose();
  };

  // Inline styles for form elements
  const inlineFormStyles = {
    container: {
      background: 'white',
      borderRadius: '8px',
      boxShadow: '0 1px 3px rgba(0, 0, 0, 0.1)',
      border: '1px solid #e5e7eb',
      padding: '1.5rem',
      opacity: isSubmitting ? 0.6 : 1,
      pointerEvents: isSubmitting ? 'none' : 'auto'
    } as React.CSSProperties,
    field: {
      marginBottom: '1rem'
    } as React.CSSProperties,
    label: {
      display: 'block',
      fontWeight: 500,
      marginBottom: '0.25rem',
      color: '#374151',
      fontSize: '0.875rem'
    } as React.CSSProperties,
    grid: {
      display: 'grid',
      gridTemplateColumns: window.innerWidth > 640 ? '1fr 1fr' : '1fr',
      gap: '1rem'
    } as React.CSSProperties,
    error: {
      display: 'block',
      marginTop: '0.25rem',
      fontSize: '0.75rem',
      color: '#ef4444'
    } as React.CSSProperties,
    buttons: {
      display: 'flex',
      gap: '0.5rem',
      justifyContent: 'flex-end',
      marginTop: '1.5rem',
      paddingTop: '1rem',
      borderTop: '1px solid #e5e7eb'
    } as React.CSSProperties,
    required: {
      color: '#ef4444'
    } as React.CSSProperties
  };

  // Form content component to avoid duplication
  const FormContent = () => (
    <form onSubmit={handleSubmit(onSubmit)} style={inline ? inlineFormStyles.container : {}}>
      <div>
        {/* Header for inline mode */}
        {inline && (
          <div style={{ marginBottom: '1rem' }}>
            <Heading level="h3" className="text-lg font-semibold text-gray-900">
              {task ? "Edit Task": "Create New Task"} 
            </Heading>
          </div>
        )}

        {/* Title */}
        <div style={inline ? inlineFormStyles.field : { marginBottom: '1rem' }}>
          <Label htmlFor="title" style={inline ? inlineFormStyles.label : {}}>
            Title <span style={inline ? inlineFormStyles.required : { color: '#ef4444' }}>*</span>
          </Label>
          <Input
            id="title"
            {...register("title")}
            placeholder="Enter task title"
            className={errors.title ? 'border-red-500' : ''}
            disabled={isSubmitting}
          />
          {errors.title && (
            <span style={inline ? inlineFormStyles.error : { display: 'block', marginTop: '0.25rem', fontSize: '0.75rem', color: '#ef4444' }}>
              {errors.title.message}
            </span>
          )}
        </div>

        {/* Description */}
        <div style={inline ? inlineFormStyles.field : { marginBottom: '1rem' }}>
          <Label htmlFor="description" style={inline ? inlineFormStyles.label : {}}>
            Description
          </Label>
          <Textarea
            id="description"
            {...register("description")}
            placeholder="Enter task description (optional)"
            rows={3}
            className={errors.description ? 'border-red-500' : ''}
            disabled={isSubmitting}
          />
          {errors.description && (
            <span style={inline ? inlineFormStyles.error : { display: 'block', marginTop: '0.25rem', fontSize: '0.75rem', color: '#ef4444' }}>
              {errors.description.message}
            </span>
          )}
        </div>

        {/* Status and Priority Row */}
        <div style={inline ? inlineFormStyles.grid : { display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '1rem', marginBottom: '1rem' }}>
          <div style={inline ? inlineFormStyles.field : {}}>
            <Label htmlFor="status" style={inline ? inlineFormStyles.label : {}}>
              Status
            </Label>
            <Controller
              name="status"
              control={control}
              render={({ field }) => (
                <Select
                  value={field.value}
                  onValueChange={field.onChange}
                  disabled={isSubmitting}
                >
                  <Select.Trigger className={`w-full ${errors.status ? 'border-red-500' : ''}`}>
                    <Select.Value placeholder="Select status" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="pending">Pending</Select.Item>
                    <Select.Item value="in_progress">In Progress</Select.Item>
                    <Select.Item value="review">Review</Select.Item>
                    <Select.Item value="completed">Completed</Select.Item>
                    <Select.Item value="cancelled">Cancelled</Select.Item>
                  </Select.Content>
                </Select>
              )}
            />
            {errors.status && (
              <span style={inline ? inlineFormStyles.error : { display: 'block', marginTop: '0.25rem', fontSize: '0.75rem', color: '#ef4444' }}>
                {errors.status.message}
              </span>
            )}
          </div>

          <div style={inline ? inlineFormStyles.field : {}}>
            <Label htmlFor="priority" style={inline ? inlineFormStyles.label : {}}>
              Priority
            </Label>
            <Controller
              name="priority"
              control={control}
              render={({ field }) => (
                <Select
                  value={field.value}
                  onValueChange={field.onChange}
                  disabled={isSubmitting}
                >
                  <Select.Trigger className={`w-full ${errors.priority ? 'border-red-500' : ''}`}>
                    <Select.Value placeholder="Select priority" />
                  </Select.Trigger>
                  <Select.Content>
                    <Select.Item value="low">Low</Select.Item>
                    <Select.Item value="medium">Medium</Select.Item>
                    <Select.Item value="high">High</Select.Item>
                    <Select.Item value="urgent">Urgent</Select.Item>
                  </Select.Content>
                </Select>
              )}
            />
            {errors.priority && (
              <span style={inline ? inlineFormStyles.error : { display: 'block', marginTop: '0.25rem', fontSize: '0.75rem', color: '#ef4444' }}>
                {errors.priority.message}
              </span>
            )}
          </div>
        </div>

        {/* Entity Information (hidden when in booking context or editing) */}
        {!isEditing && !isBookingContext && (
          <div style={inline ? inlineFormStyles.grid : { display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '1rem', marginBottom: '1rem' }}>
            <div style={inline ? inlineFormStyles.field : {}}>
              <Label htmlFor="entity_type" style={inline ? inlineFormStyles.label : {}}>
                Entity Type
              </Label>
              <Controller
                name="entity_type"
                control={control}
                render={({ field }) => (
                  <Select
                    value={field.value}
                    onValueChange={field.onChange}
                    disabled={isSubmitting}
                  >
                    <Select.Trigger className={`w-full ${errors.entity_type ? 'border-red-500' : ''}`}>
                      <Select.Value placeholder="Select entity type" />
                    </Select.Trigger>
                    <Select.Content>
                      <Select.Item value="general">General Task</Select.Item>
                      <Select.Item value="booking">Booking</Select.Item>
                      <Select.Item value="deal">Deal</Select.Item>
                      <Select.Item value="guest">Guest</Select.Item>
                      <Select.Item value="itinerary">Itinerary</Select.Item>
                    </Select.Content>
                  </Select>
                )}
              />
              {errors.entity_type && (
                <span style={inline ? inlineFormStyles.error : { display: 'block', marginTop: '0.25rem', fontSize: '0.75rem', color: '#ef4444' }}>
                  {errors.entity_type.message}
                </span>
              )}
            </div>

            <div style={inline ? inlineFormStyles.field : {}}>
              <Label htmlFor="entity_id" style={inline ? inlineFormStyles.label : {}}>
                Entity ID
              </Label>
              <Input
                id="entity_id"
                {...register("entity_id")}
                placeholder="Enter entity ID (optional)"
                className={errors.entity_id ? 'border-red-500' : ''}
                disabled={isSubmitting}
              />
              {errors.entity_id && (
                <span style={inline ? inlineFormStyles.error : { display: 'block', marginTop: '0.25rem', fontSize: '0.75rem', color: '#ef4444' }}>
                  {errors.entity_id.message}
                </span>
              )}
            </div>
          </div>
        )}

        {/* Assignment and Due Date Row */}
        <div style={inline ? inlineFormStyles.grid : { display: 'grid', gridTemplateColumns: 'repeat(2, 1fr)', gap: '1rem', marginBottom: '1rem' }}>
          <div style={inline ? inlineFormStyles.field : {}}>
            <Label htmlFor="assigned_to" style={inline ? inlineFormStyles.label : {}}>
              Assigned To
            </Label>
            <UserSelector
              value={watch("assigned_to")}
              onChange={(value) => setValue("assigned_to", value || "")}
              placeholder="Select a user (optional)"
              disabled={isSubmitting}
              className={errors.assigned_to ? 'border-red-500' : ''}
            />
            {errors.assigned_to && (
              <span style={inline ? inlineFormStyles.error : { display: 'block', marginTop: '0.25rem', fontSize: '0.75rem', color: '#ef4444' }}>
                {errors.assigned_to.message}
              </span>
            )}
          </div>

          <div style={inline ? inlineFormStyles.field : {}}>
            <Label htmlFor="due_date" style={inline ? inlineFormStyles.label : {}}>
              Due Date
            </Label>
            <Input
              id="due_date"
              type="date"
              {...register("due_date")}
              className={errors.due_date ? 'border-red-500' : ''}
              disabled={isSubmitting}
            />
            {errors.due_date && (
              <span style={inline ? inlineFormStyles.error : { display: 'block', marginTop: '0.25rem', fontSize: '0.75rem', color: '#ef4444' }}>
                {errors.due_date.message}
              </span>
            )}
          </div>
        </div>
      </div>

      <div style={inline ? inlineFormStyles.buttons : { display: 'flex', gap: '0.5rem', justifyContent: 'flex-end', marginTop: '1.5rem', paddingTop: '1rem', borderTop: '1px solid #e5e7eb' }}>
        <Button
          type="button"
          variant="secondary"
          onClick={handleClose}
          disabled={isSubmitting}
          size={inline ? "small" : "medium"}
        >
          Cancel
        </Button>
        <Button
          type="submit"
          disabled={isSubmitting}
          isLoading={isSubmitting}
          size={inline ? "small" : "medium"}
        >
          {isEditing ? "Update Task" : "Create Task"}
        </Button>
      </div>
    </form>
  );

  if (inline) {
    return <FormContent />;
  }

  // Default: modal rendering
  return (
    <FocusModal open={isOpen} onOpenChange={handleClose}>
      <FocusModal.Content>
        <FocusModal.Header>
          <Heading level="h2">
            {isEditing ? "Edit Task" : "Create New Task"}
          </Heading>
        </FocusModal.Header>
        <FormContent />
      </FocusModal.Content>
    </FocusModal>
  );
};