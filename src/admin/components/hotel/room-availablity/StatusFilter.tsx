import React from "react";
import { MultiSelect } from "../../common/MultiSelect";
import { RoomInventoryStatus } from "../types";
import { getStatusDisplayName } from "./statusUtils";

interface StatusFilterProps {
  selectedStatuses: RoomInventoryStatus[];
  onStatusesChange: (statuses: RoomInventoryStatus[]) => void;
}

const StatusFilter: React.FC<StatusFilterProps> = ({
  selectedStatuses,
  onStatusesChange,
}) => {
  // Use only the statuses that are actually shown in the UI (after API mapping)
  const uiStatuses = [
    RoomInventoryStatus.AVAILABLE,
    RoomInventoryStatus.BOOKED,
    RoomInventoryStatus.RESERVED,
    RoomInventoryStatus.MAINTENANCE,
    RoomInventoryStatus.UNAVAILABLE,
    RoomInventoryStatus.ON_DEMAND,
    RoomInventoryStatus.ON_HOLD,
  ];

  const options = uiStatuses.map((status) => ({
    value: status,
    label: getStatusDisplayName(status),
  }));

  const handleChange = (values: string[]) => {
    onStatusesChange(values as RoomInventoryStatus[]);
  };

  return (
    <div className="flex flex-col">
      <label className="text-xs text-muted-foreground mb-1">Status</label>
      <MultiSelect
        options={options}
        selectedValues={selectedStatuses}
        onChange={handleChange}
        placeholder="All Statuses"
        className="min-w-[240px]"
        showSelectAll={true}
        showSelectedTags={false}
      />
    </div>
  );
};

export default StatusFilter;
