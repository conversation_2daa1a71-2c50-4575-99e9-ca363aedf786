import { z } from "zod";

// Valid room inventory status values
export const RoomInventoryStatusEnum = z.enum([
  "available",
  "reserved",
  "booked",
  "maintenance",
  "cleaning",
  "unavailable",
  "reserved_unassigned",
  "cart_reserved",
  "on_demand",
  "on_hold",
]);

export const PostAdminCreateOrUpdateRoomInventory = z.object({
  id: z.string().optional(),
  inventory_item_id: z.string(),
  from_date: z.string().or(z.date()),
  to_date: z.string().or(z.date()),
  notes: z.string().optional(),
  is_available: z.boolean().default(true),
  available_quantity: z.number().default(1),
  status: RoomInventoryStatusEnum.optional(),
  check_in_time: z.string().default("12:00"),
  check_out_time: z.string().default("12:00"),
  is_noon_to_noon: z.boolean().default(true),
  dynamic_price: z.number().optional(),
});

// Schema for updating room availability type
export const PostAdminUpdateRoomAvailabilityType = z.object({
  availability_type: z.enum(["standard", "on_demand"]),
});

// Schema for on-demand room status updates
export const PostAdminUpdateOnDemandRoomStatus = z.object({
  status: z.enum(["on_demand", "booked", "available"]),
  start_date: z.string().or(z.date()),
  end_date: z.string().or(z.date()),
  notes: z.string().optional(),
  customer_info: z.record(z.string()).optional(),
});
