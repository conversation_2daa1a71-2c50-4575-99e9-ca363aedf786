import { useQuery } from "@tanstack/react-query";

// Types for relationship data
export interface OrderLineItem {
  id: string;
  unit_price: number;
  quantity: number;
  title: string;
  variant_id: string | null;
  metadata: Record<string, any> | null;
}

export interface OrderItem {
  id: string;
  quantity: number;
  unit_price: number;
  title: string;
  variant_id: string | null;
  metadata: Record<string, any> | null;
}

export interface ConciergeOrder {
  id: string;
  order_id: string;
  assigned_to: string | null;
  notes: string | null;
  status: string;
  last_contacted_at: string | null;
  created_at: string;
  updated_at: string;
}

export interface Order {
  id: string;
  display_id: number;
  email: string | null;
  currency_code: string;
  total: number;
  created_at: string;
  updated_at: string;
}

// Types for ConciergeOrderItem (On-Request Items) with relationship data
export interface ConciergeOrderItem {
  id: string;
  concierge_order_id: string;
  line_item_id: string | null;
  item_id: string | null;
  variant_id: string | null;
  quantity: number;
  unit_price: number;
  title: string;
  status: "under_review" | "client_confirmed" | "order_placed" | "cancelled" | "completed";
  is_active: boolean;
  added_by: string | null;
  finalized_by: string | null;
  added_at: string;
  finalized_at: string | null;
  metadata: Record<string, any> | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  // New fields
  category_id: string | null;
  start_date: string | null;
  end_date: string | null;
  // Relationship data
  // Enhanced relationship data with proper database schema mapping
  request_id?: string; // concierge_order_item.id
  hotel?: string; // concierge_order.hotel_id → hotel.name
  category?: string; // concierge_order_item.category_id → product_service_category.name
  total_price?: number; // quantity * unit_price
  booking?: string; // concierge_order_item.concierge_order_id
  customer?: string; // concierge_order.order_id → order.customer_id → customer.first_name + ' ' + customer.last_name
  check_in_date?: string; // concierge_order.check_in_date
  check_out_date?: string; // concierge_order.check_out_date

  // Legacy relationship data
  order_line_item?: OrderLineItem;
  order_item?: OrderItem;
  concierge_order?: ConciergeOrder;
  order?: Order;
}

export interface OnRequestItemsFilters {
  limit?: number;
  offset?: number;
  status?: "under_review" | "client_confirmed" | "order_placed" | "cancelled" | "completed";
  exclude_completed?: boolean;
  is_active?: boolean;
  line_item_id?: string;
  item_id?: string;
  variant_id?: string;
  concierge_order_id?: string;
  added_by?: string;
  finalized_by?: string;
  search?: string;
  order?: string;
  sort_order?: "asc" | "desc";
  created_at_gte?: string;
  created_at_lte?: string;
}

export interface OnRequestItemsResponse {
  concierge_order_items: ConciergeOrderItem[];
  count: number;
  limit: number;
  offset: number;
}

// Query Keys
export const onRequestItemsKeys = {
  all: ["on-request-items"] as const,
  lists: () => [...onRequestItemsKeys.all, "list"] as const,
  list: (filters: OnRequestItemsFilters) =>
    [...onRequestItemsKeys.lists(), filters] as const,
};

// API function to fetch on-request items
const fetchOnRequestItems = async (filters: OnRequestItemsFilters = {}): Promise<OnRequestItemsResponse> => {
  const params = new URLSearchParams();

  // Add all filter parameters to the URL
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== "") {
      params.append(key, String(value));
    }
  });

  const url = `/admin/supplier-management/on-request${
    params.toString() ? `?${params.toString()}` : ""
  }`;

  console.log("🔍 Fetching on-request items from:", url);

  const response = await fetch(url, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
    },
    credentials: "include",
  });

  if (!response.ok) {
    const error = await response.json();
    throw new Error(error.message || `HTTP ${response.status}: ${response.statusText}`);
  }

  const data = await response.json();
  console.log("✅ On-request items fetched successfully:", data);
  
  return data;
};

// React Query hook
export const useOnRequestItems = (filters: OnRequestItemsFilters = {}) => {
  return useQuery({
    queryKey: onRequestItemsKeys.list(filters),
    queryFn: () => fetchOnRequestItems(filters),
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
    refetchOnMount: true,
  });
};

// Hook for getting on-request items with default pagination
export const useOnRequestItemsPaginated = (
  page: number = 1,
  pageSize: number = 20,
  additionalFilters: Omit<OnRequestItemsFilters, 'limit' | 'offset'> = {}
) => {
  const filters: OnRequestItemsFilters = {
    ...additionalFilters,
    limit: pageSize,
    offset: (page - 1) * pageSize,
  };

  return useOnRequestItems(filters);
};

// Hook for getting active on-request items only
export const useActiveOnRequestItems = (filters: Omit<OnRequestItemsFilters, 'is_active'> = {}) => {
  return useOnRequestItems({
    ...filters,
    is_active: true,
  });
};

// Hook for getting on-request items by status
export const useOnRequestItemsByStatus = (
  status: "under_review" | "client_confirmed" | "order_placed" | "cancelled" | "completed",
  filters: Omit<OnRequestItemsFilters, 'status'> = {}
) => {
  return useOnRequestItems({
    ...filters,
    status,
  });
};

// Hook for getting actionable on-request items (excluding completed)
export const useActionableOnRequestItems = (filters: OnRequestItemsFilters = {}) => {
  return useOnRequestItems({
    ...filters,
    // Add a filter to exclude completed items by default
    // This will be handled in the API endpoint
  });
};

// Hook for getting client confirmed items (primary focus for supplier team)
export const useClientConfirmedItems = (filters: Omit<OnRequestItemsFilters, 'status'> = {}) => {
  return useOnRequestItemsByStatus("client_confirmed", filters);
};

// Individual item response interface
export interface ConciergeOrderItemDetailResponse {
  concierge_order_item: ConciergeOrderItem;
}

// Hook for getting a single on-request item by ID
export const useOnRequestItem = (itemId: string) => {
  return useQuery({
    queryKey: ["on-request-item", itemId],
    queryFn: async (): Promise<ConciergeOrderItemDetailResponse> => {
      if (!itemId) {
        throw new Error("Item ID is required");
      }

      // First, get the item from our list API to get the concierge_order_id
      const listResponse = await fetch(`/admin/supplier-management/on-request?limit=1&search=${itemId}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
      });

      if (!listResponse.ok) {
        throw new Error(`Failed to fetch item: ${listResponse.status} ${listResponse.statusText}`);
      }

      const listData = await listResponse.json();
      const items = listData.concierge_order_items || [];
      const item = items.find((i: ConciergeOrderItem) => i.id === itemId);

      if (!item) {
        throw new Error("Item not found");
      }

      // Now use the detailed API endpoint
      const detailResponse = await fetch(
        `/admin/concierge-management/orders/${item.concierge_order_id}/items/${itemId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
          credentials: "include",
        }
      );

      if (!detailResponse.ok) {
        throw new Error(`Failed to fetch item details: ${detailResponse.status} ${detailResponse.statusText}`);
      }

      const detailData = await detailResponse.json();
      console.log("✅ On-request item details fetched successfully:", detailData);

      return detailData;
    },
    enabled: !!itemId,
    staleTime: 30000, // 30 seconds
    refetchOnWindowFocus: false,
  });
};
