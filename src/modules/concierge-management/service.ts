import {
  MedusaService,
  MedusaError,
  Modules,
} from "@camped-ai/framework/utils";
import { ConciergeTask } from "./models/concierge-task";
import { Note } from "./models/note";
import { ConciergeOrder } from "./models/concierge-order";
import { ConciergeOrderItem } from "./models/concierge-order-item";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../supplier-products-services";
import {
  ConciergeManagementServiceMethods,
  ConciergeTaskResponse,
  CreateConciergeTaskRequest,
  UpdateConciergeTaskRequest,
  ListConciergeTasksRequest,
  ConciergeTaskListResponse,
  TaskStatus,
  NoteResponse,
  CreateNoteRequest,
  UpdateNoteRequest,
  ListNotesRequest,
  NoteListResponse,
  CreateConciergeOrderInput,
  UpdateConciergeOrderInput,
  ConciergeOrderResponse,
  ConciergeOrderFilters,
  ConciergeOrderListOptions,
  ConciergeOrderListResponse,
  ConciergeOrderStatus,
  CreateConciergeOrderItemInput,
  UpdateConciergeOrderItemInput,
  ConciergeOrderItemResponse,
  ConciergeOrderItemFilters,
  ConciergeOrderItemListOptions,
  ConciergeOrderItemListResponse,
  ConciergeOrderItemStatus,
} from "./types";

/**
 * Concierge Management Service
 *
 * Provides comprehensive concierge management functionality including
 * task management, assignments, and entity linking.
 */
class ConciergeManagementService
  extends MedusaService({
    ConciergeTask,
    Note,
    ConciergeOrder,
    ConciergeOrderItem,
  })
  implements ConciergeManagementServiceMethods
{
  protected container_: any;

  constructor(container: any) {
    super(container);
    this.container_ = container;
  }


  /**
   * Create category lookup map from passed category data
   */
  private createCategoryLookupMapFromData(categories: any[]): Record<string, any> {
    try {
      console.log(`🔍 [DEBUG] Building category lookup map from ${categories.length} passed categories`);

      if (!categories || categories.length === 0) {
        console.warn(`⚠️ [DEBUG] No categories provided for lookup map`);
        return {};
      }

      // Build lookup map from passed category data
      const categoryMap: Record<string, any> = {};
      categories.forEach((category: any) => {
        categoryMap[category.id] = {
          id: category.id,
          name: category.name,
          description: category.description || null,
          icon: category.icon || null
        };
      });

      console.log(`✅ [DEBUG] Category lookup map built with ${Object.keys(categoryMap).length} categories`);
      console.log(`🔍 [DEBUG] Category IDs in lookup map:`, Object.keys(categoryMap));
      return categoryMap;
    } catch (error) {
      console.warn(`❌ [DEBUG] Failed to build category lookup map from data:`, error.message);
      return {};
    }
  }


  /**
   * Pre-fetch categories and create lookup map - using supplier products services module (DEPRECATED)
   */
  private async createCategoryLookupMap(): Promise<Record<string, any>> {
    try {
      console.log(
        `🔍 [DEBUG] Pre-fetching categories using supplier products services module`
      );

      // Resolve the supplier products services module - same approach as working APIs
      const supplierProductsServicesService = this.container_.resolve(
        SUPPLIER_PRODUCTS_SERVICES_MODULE
      );

      if (!supplierProductsServicesService) {
        console.warn(
          `⚠️ [DEBUG] Supplier products services module not available for category lookup`
        );
        return {};
      }

      console.log(
        `✅ [DEBUG] Supplier products services module resolved successfully`
      );

      // Use listCategories method - same approach as working APIs
      const categories = await supplierProductsServicesService.listCategories(
        { is_active: true },
        { skip: 0, take: 100 }
      );

      console.log(
        `🔍 [DEBUG] Raw categories data from listCategories:`,
        categories
      );

      if (!categories || categories.length === 0) {
        console.warn(`⚠️ [DEBUG] No categories found via listCategories`);
        return {};
      }

      console.log(
        `🔍 [DEBUG] Found ${categories.length} categories via listCategories`
      );

      // Build lookup map from service results
      const categoryMap: Record<string, any> = {};
      categories.forEach((category: any) => {
        categoryMap[category.id] = {
          id: category.id,
          name: category.name,
          description: category.description || null,
          icon: category.icon || null,
        };
      });

      console.log(
        `✅ [DEBUG] Category lookup map built with ${
          Object.keys(categoryMap).length
        } categories`
      );
      console.log(
        `🔍 [DEBUG] Category IDs in lookup map:`,
        Object.keys(categoryMap)
      );
      return categoryMap;
    } catch (error) {
      console.warn(
        `❌ [DEBUG] Failed to pre-fetch categories via service:`,
        error.message
      );
      console.warn(`❌ [DEBUG] Error details:`, error);
      return {};
    }
  }

  /**
   * Create a new concierge task
   */
  async createTask(
    data: CreateConciergeTaskRequest
  ): Promise<ConciergeTaskResponse> {
    try {
      const taskData = {
        ...data.task,
        is_deleted: false,
      };

      const tasks = await this.createConciergeTasks([taskData]);
      return tasks[0] as ConciergeTaskResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create concierge task: ${error.message}`
      );
    }
  }

  /**
   * Update an existing concierge task
   */
  async updateTask(
    id: string,
    data: UpdateConciergeTaskRequest
  ): Promise<ConciergeTaskResponse> {
    try {
      const updateData = {
        ...data.task,
      };

      const tasks = await this.updateConciergeTasks([
        {
          id,
          ...updateData,
        },
      ]);

      return tasks[0] as ConciergeTaskResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge task: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a single concierge task
   */
  async retrieveTask(id: string): Promise<ConciergeTaskResponse> {
    try {
      const task = await this.retrieveConciergeTask(id);

      if (!task) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Task with id ${id} not found`
        );
      }

      return task as ConciergeTaskResponse;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve concierge task: ${error.message}`
      );
    }
  }

  /**
   * List concierge tasks with filtering and pagination
   */
  async listTasks(
    request: ListConciergeTasksRequest
  ): Promise<ConciergeTaskListResponse> {
    try {
      const { filters = {}, options = {} } = request;
      const {
        limit = 20,
        offset = 0,
        order = { created_at: "DESC" },
      } = options;

      // Build filters - exclude soft deleted by default
      const queryFilters = {
        ...filters,
        is_deleted:
          filters.is_deleted !== undefined ? filters.is_deleted : false,
      };

      const tasks = await this.listConciergeTasks(queryFilters, {
        skip: offset,
        take: limit,
        order,
      });

      // Get count separately
      const allTasks = await this.listConciergeTasks(queryFilters);
      const count = Array.isArray(allTasks) ? allTasks.length : 0;

      return {
        tasks: tasks as ConciergeTaskResponse[],
        count,
        limit,
        offset,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list concierge tasks: ${error.message}`
      );
    }
  }

  /**
   * Hard delete a concierge task
   */
  async deleteTask(id: string): Promise<void> {
    try {
      await this.deleteConciergeTasks([id]);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete concierge task: ${error.message}`
      );
    }
  }

  /**
   * Soft delete a concierge task
   */
  async softDeleteTask(
    id: string,
    deletedBy?: string
  ): Promise<ConciergeTaskResponse> {
    try {
      const updateData = {
        is_deleted: true,
        deleted_at: new Date(),
        ...(deletedBy && { updated_by: deletedBy }),
      };

      const tasks = await this.updateConciergeTasks([
        {
          id,
          ...updateData,
        },
      ]);

      return tasks[0] as ConciergeTaskResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to soft delete concierge task: ${error.message}`
      );
    }
  }

  /**
   * Assign a task to a user
   */
  async assignTask(
    taskId: string,
    userId: string,
    assignedBy?: string
  ): Promise<ConciergeTaskResponse> {
    return await this.updateTask(taskId, {
      task: {
        assigned_to: userId,
        status: TaskStatus.IN_PROGRESS,
        ...(assignedBy && { updated_by: assignedBy }),
      },
    });
  }

  /**
   * Unassign a task from a user
   */
  async unassignTask(
    taskId: string,
    unassignedBy?: string
  ): Promise<ConciergeTaskResponse> {
    return await this.updateTask(taskId, {
      task: {
        assigned_to: undefined,
        status: TaskStatus.PENDING,
        ...(unassignedBy && { updated_by: unassignedBy }),
      },
    });
  }

  /**
   * Mark a task as completed
   */
  async completeTask(
    taskId: string,
    completedBy?: string
  ): Promise<ConciergeTaskResponse> {
    return await this.updateTask(taskId, {
      task: {
        status: TaskStatus.COMPLETED,
        ...(completedBy && { updated_by: completedBy }),
      },
    });
  }

  /**
   * Cancel a task
   */
  async cancelTask(
    taskId: string,
    cancelledBy?: string
  ): Promise<ConciergeTaskResponse> {
    return await this.updateTask(taskId, {
      task: {
        status: TaskStatus.CANCELLED,
        ...(cancelledBy && { updated_by: cancelledBy }),
      },
    });
  }

  /**
   * Get all tasks for a specific entity
   */
  async getTasksForEntity(
    entityType: string,
    entityId: string
  ): Promise<ConciergeTaskResponse[]> {
    try {
      const result = await this.listTasks({
        filters: {
          entity_type: entityType,
          entity_id: entityId,
          is_deleted: false,
        },
        options: {
          order: { created_at: "DESC" },
          limit: 100,
        },
      });

      return result.tasks;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get tasks for entity: ${error.message}`
      );
    }
  }

  /**
   * Get all tasks assigned to a specific user
   */
  async getTasksForUser(userId: string): Promise<ConciergeTaskResponse[]> {
    try {
      const result = await this.listTasks({
        filters: {
          assigned_to: userId,
          is_deleted: false,
        },
        options: {
          order: { due_date: "ASC", priority: "DESC" },
          limit: 100,
        },
      });

      return result.tasks;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get tasks for user: ${error.message}`
      );
    }
  }

  // ===== NOTE MANAGEMENT METHODS =====

  /**
   * Create a new note
   */
  async createNote(data: CreateNoteRequest): Promise<NoteResponse> {
    try {
      const noteData = {
        ...data.note,
        deleted: false,
      };

      const notes = await this.createNotes([noteData]);
      return notes[0] as NoteResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create note: ${error.message}`
      );
    }
  }

  /**
   * Update an existing note
   */
  async updateNote(id: string, data: UpdateNoteRequest): Promise<NoteResponse> {
    try {
      const updateData = {
        ...data.note,
      };

      const notes = await this.updateNotes([
        {
          id,
          ...updateData,
        },
      ]);

      return notes[0] as NoteResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update note: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a single note by ID
   */
  async getNote(id: string): Promise<NoteResponse> {
    try {
      const [notes] = await this.listAndCountNotes({ id });
      const note = notes[0];

      if (!note || note.deleted) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Note with id ${id} not found`
        );
      }

      return note as NoteResponse;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve note: ${error.message}`
      );
    }
  }

  /**
   * List notes with filtering and pagination
   */
  async getNotes(request: ListNotesRequest): Promise<NoteListResponse> {
    try {
      const { filters = {}, options = {} } = request;
      const {
        limit = 20,
        offset = 0,
        order = { created_at: "DESC" },
      } = options;

      // Build filters - exclude soft deleted by default
      const queryFilters = {
        ...filters,
        deleted: filters.deleted !== undefined ? filters.deleted : false,
      };

      const [notes, count] = await this.listAndCountNotes(queryFilters, {
        skip: offset,
        take: limit,
        order,
      });

      return {
        notes: notes as NoteResponse[],
        count,
        limit,
        offset,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list notes: ${error.message}`
      );
    }
  }

  /**
   * Hard delete a note
   */
  async deleteNote(id: string): Promise<void> {
    try {
      await this.deleteNotes([id]);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete note: ${error.message}`
      );
    }
  }

  /**
   * Soft delete a note
   */
  async softDeleteNote(id: string, deletedBy?: string): Promise<NoteResponse> {
    return await this.updateNote(id, {
      note: {
        deleted: true,
        ...(deletedBy && { updated_by_id: deletedBy }),
      },
    });
  }

  /**
   * Get all notes for a specific entity
   */
  async getNotesForEntity(
    entity: string,
    entityId: string
  ): Promise<NoteResponse[]> {
    try {
      const result = await this.getNotes({
        filters: {
          entity,
          entity_id: entityId,
          deleted: false,
        },
        options: {
          order: { created_at: "DESC" },
          limit: 100,
        },
      });

      return result.notes;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to get notes for entity: ${error.message}`
      );
    }
  }

  // ===== CONCIERGE ORDER METHODS =====

  /**
   * Create a new concierge order
   */
  async createConciergeOrder(
    data: CreateConciergeOrderInput
  ): Promise<ConciergeOrderResponse> {
    try {
      const orderData = {
        ...data,
        status: data.status || ConciergeOrderStatus.NOT_STARTED,
      };

      const orders = await this.createConciergeOrders([orderData]);
      return orders[0] as ConciergeOrderResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create concierge order: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a concierge order by ID
   */
  async retrieveConciergeOrder(
    id: string,
    config?: any
  ): Promise<ConciergeOrderResponse> {
    try {
      const orders = await this.listConciergeOrders(
        { id },
        { take: 1, ...config }
      );
      if (!orders || orders.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Concierge order with id: ${id} was not found`
        );
      }
      return orders[0] as ConciergeOrderResponse;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve concierge order: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a concierge order by order ID
   */
  async retrieveConciergeOrderByOrderId(
    orderId: string,
    config?: any
  ): Promise<ConciergeOrderResponse | null> {
    try {
      const orders = await this.listConciergeOrders(
        { order_id: orderId },
        { take: 1, ...config }
      );
      return orders.length > 0 ? orders[0] : null;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve concierge order by order ID: ${error.message}`
      );
    }
  }

  /**
   * Update a concierge order
   */
  async updateConciergeOrder(
    id: string,
    data: UpdateConciergeOrderInput
  ): Promise<ConciergeOrderResponse> {
    try {
      const updateData = {
        ...data,
        ...(data.last_contacted_at && {
          last_contacted_at: new Date(data.last_contacted_at),
        }),
      };

      await this.updateConciergeOrders([{ id, ...updateData }]);
      return await this.retrieveConciergeOrder(id);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge order: ${error.message}`
      );
    }
  }

  /**
   * List concierge orders with filters and pagination
   */
  async listConciergeOrdersWithPagination(
    filters: ConciergeOrderFilters = {},
    options: ConciergeOrderListOptions = {}
  ): Promise<ConciergeOrderListResponse> {
    try {
      const { limit = 20, offset = 0, ...listOptions } = options;

      const orders = await this.listConciergeOrders(filters, {
        take: limit,
        skip: offset,
        ...listOptions,
      });

      // Get total count for pagination
      const totalCount = await this.listConciergeOrders(filters, {
        take: undefined,
        skip: undefined,
      });

      return {
        concierge_orders: orders,
        count: totalCount.length,
        limit,
        offset,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list concierge orders: ${error.message}`
      );
    }
  }

  /**
   * List concierge orders with combined order data (from both concierge_orders and orders tables)
   */
  async listConciergeOrdersWithOrderData(
    filters: ConciergeOrderFilters = {},
    options: ConciergeOrderListOptions = {}
  ): Promise<any> {
    try {
      const { limit = 20, offset = 0, ...listOptions } = options;

      // Extract search parameter if present
      const searchQuery = (filters as any).search;
      const cleanFilters = { ...filters };
      delete (cleanFilters as any).search;

      // Get concierge orders first
      const conciergeOrders = await this.listConciergeOrders(cleanFilters, {
        take: limit,
        skip: offset,
        ...listOptions,
      });

      // Get total count for pagination
      const totalCount = await this.listConciergeOrders(cleanFilters, {
        take: undefined,
        skip: undefined,
      });

      // If no concierge orders found, return empty result
      if (!conciergeOrders || conciergeOrders.length === 0) {
        return {
          concierge_orders: [],
          count: 0,
          limit,
          offset,
        };
      }

      // Extract order IDs from concierge orders
      const orderIds = conciergeOrders
        .map((co: any) => co.order_id)
        .filter((id: string) => id);

      // Get order data using the order module
      let ordersData: any[] = [];
      if (orderIds.length > 0) {
        try {
          console.log(
            `[ConciergeService] Fetching order data for ${orderIds.length} orders`
          );
          console.log(
            `[ConciergeService] Container available:`,
            !!this.container_
          );
          console.log(`[ConciergeService] Modules.ORDER value:`, Modules.ORDER);

          // Resolve the order service using the correct container and module reference
          const orderService = this.container_.resolve(Modules.ORDER);
          console.log(
            `[ConciergeService] Order service resolved:`,
            !!orderService
          );

          // Fetch orders with related data
          const orderPromises = orderIds.map(async (orderId: string) => {
            try {
              const order = await orderService.retrieveOrder(orderId, {
                relations: [
                  "customer",
                  "items",
                  "shipping_address",
                  "billing_address",
                  "payments",
                  "fulfillments",
                ],
              });
              console.log(
                `[ConciergeService] Successfully fetched order ${orderId}`
              );
              return order;
            } catch (error) {
              console.warn(
                `[ConciergeService] Failed to fetch order ${orderId}:`,
                error.message
              );
              return null;
            }
          });

          const orderResults = await Promise.all(orderPromises);
          ordersData = orderResults.filter((order) => order !== null);
          console.log(
            `[ConciergeService] Successfully fetched ${ordersData.length} out of ${orderIds.length} orders`
          );
        } catch (error) {
          console.warn(
            "[ConciergeService] Failed to fetch order data:",
            error.message
          );
          console.warn("[ConciergeService] Error details:", error);
          // Continue without order data if there's an error
        }
      }

      // Combine concierge orders with order data
      let combinedOrders = conciergeOrders.map((conciergeOrder: any) => {
        const orderData = ordersData.find(
          (order) => order.id === conciergeOrder.order_id
        );

        return {
          ...conciergeOrder,
          order: orderData || null,
        };
      });

      // Apply search filter if provided
      if (searchQuery && searchQuery.trim()) {
        const searchTerm = searchQuery.toLowerCase().trim();
        combinedOrders = combinedOrders.filter((item: any) => {
          // Search in concierge order fields
          const conciergeMatch =
            (item.notes && item.notes.toLowerCase().includes(searchTerm)) ||
            (item.assigned_to &&
              item.assigned_to.toLowerCase().includes(searchTerm)) ||
            (item.status && item.status.toLowerCase().includes(searchTerm));

          // Search in order fields if order data is available
          const orderMatch =
            item.order &&
            ((item.order.email &&
              item.order.email.toLowerCase().includes(searchTerm)) ||
              (item.order.display_id &&
                item.order.display_id.toString().includes(searchTerm)) ||
              (item.order.customer &&
                item.order.customer.first_name &&
                item.order.customer.first_name
                  .toLowerCase()
                  .includes(searchTerm)) ||
              (item.order.customer &&
                item.order.customer.last_name &&
                item.order.customer.last_name
                  .toLowerCase()
                  .includes(searchTerm)) ||
              (item.order.customer &&
                item.order.customer.email &&
                item.order.customer.email.toLowerCase().includes(searchTerm)));

          return conciergeMatch || orderMatch;
        });
      }

      return {
        concierge_orders: combinedOrders,
        count: searchQuery ? combinedOrders.length : totalCount.length,
        limit,
        offset,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list concierge orders with order data: ${error.message}`
      );
    }
  }

  /**
   * Assign a concierge order to a user
   */
  async assignConciergeOrder(
    id: string,
    assignedTo: string,
    assignedBy?: string
  ): Promise<ConciergeOrderResponse> {
    try {
      return await this.updateConciergeOrder(id, {
        assigned_to: assignedTo,
        status: ConciergeOrderStatus.IN_PROGRESS,
        metadata: {
          assigned_by: assignedBy,
          assigned_at: new Date().toISOString(),
        },
      });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to assign concierge order: ${error.message}`
      );
    }
  }

  /**
   * Update concierge order status
   */
  async updateConciergeOrderStatus(
    id: string,
    status: ConciergeOrderStatus,
    updatedBy?: string
  ): Promise<ConciergeOrderResponse> {
    try {
      return await this.updateConciergeOrder(id, {
        status,
        metadata: {
          status_updated_by: updatedBy,
          status_updated_at: new Date().toISOString(),
        },
      });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge order status: ${error.message}`
      );
    }
  }

  /**
   * Delete a concierge order (soft delete)
   */
  async deleteConciergeOrder(id: string): Promise<void> {
    try {
      await this.softDeleteConciergeOrders([id]);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete concierge order: ${error.message}`
      );
    }
  }

  // ===== CONCIERGE ORDER ITEM METHODS =====

  /**
   * Create a new concierge order item
   */
  async createConciergeOrderItem(
    data: CreateConciergeOrderItemInput
  ): Promise<ConciergeOrderItemResponse> {
    try {
      const itemData = {
        ...data,
        status: data.status || ConciergeOrderItemStatus.UNDER_REVIEW,
        added_at: new Date(),
        is_active: data.is_active !== undefined ? data.is_active : true,
      };

      const items = await this.createConciergeOrderItems([itemData]);
      return items[0] as ConciergeOrderItemResponse;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create concierge order item: ${error.message}`
      );
    }
  }

  /**
   * Retrieve a concierge order item by ID
   */
  async retrieveConciergeOrderItem(
    id: string,
    config?: any
  ): Promise<ConciergeOrderItemResponse> {
    try {
      const items = await this.listConciergeOrderItems(
        { id },
        { take: 1, ...config }
      );
      if (!items || items.length === 0) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Concierge order item with id: ${id} was not found`
        );
      }
      return items[0] as ConciergeOrderItemResponse;
    } catch (error) {
      if (error instanceof MedusaError) {
        throw error;
      }
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve concierge order item: ${error.message}`
      );
    }
  }

  /**
   * Update a concierge order item
   */
  async updateConciergeOrderItem(
    id: string,
    data: UpdateConciergeOrderItemInput
  ): Promise<ConciergeOrderItemResponse> {
    try {
      const updateData = {
        ...data,
        ...(data.finalized_at && { finalized_at: new Date(data.finalized_at) }),
      };

      await this.updateConciergeOrderItems([{ id, ...updateData }]);
      return await this.retrieveConciergeOrderItem(id);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge order item: ${error.message}`
      );
    }
  }

  /**
   * List concierge order items with filters and pagination
   */
  async listConciergeOrderItemsWithPagination(
    filters: ConciergeOrderItemFilters = {},
    options: ConciergeOrderItemListOptions = {}
  ): Promise<ConciergeOrderItemListResponse> {
    try {
      const { limit = 20, offset = 0, ...listOptions } = options;

      const items = await this.listConciergeOrderItems(filters, {
        take: limit,
        skip: offset,
        ...listOptions,
      });

      // Get total count for pagination
      const totalCount = await this.listConciergeOrderItems(filters, {
        take: undefined,
        skip: undefined,
      });

      return {
        concierge_order_items: items,
        count: totalCount.length,
        limit,
        offset,
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list concierge order items: ${error.message}`
      );
    }
  }

  /**
   * List concierge order items with enhanced relationship data for supplier management
   *
   * Enhanced Database Schema and Column Mapping:
   * 1. request_id: concierge_order_item.id
   * 2. hotel: concierge_order.hotel_id → hotel.name
   * 3. category: concierge_order_item.category_id → product_service_category.name
   * 4. title: concierge_order_item.title
   * 5. quantity: concierge_order_item.quantity
   * 6. unit_price: concierge_order_item.unit_price
   * 7. total_price: quantity * unit_price
   * 8. booking: concierge_order_item.concierge_order_id
   * 9. customer: concierge_order.order_id → order.customer_id → customer.first_name + ' ' + customer.last_name
   * 10. check_in_date: concierge_order.check_in_date
   * 11. check_out_date: concierge_order.check_out_date
   */
  async listConciergeOrderItemsWithRelationships(
    filters: ConciergeOrderItemFilters = {},
    options: ConciergeOrderItemListOptions = {},
    categories: any[] = []
  ): Promise<any> {
    try {
      const { limit = 20, offset = 0, ...listOptions } = options;

      console.log(
        "🔍 [DEBUG] Enhanced listConciergeOrderItemsWithRelationships called with filters:",
        JSON.stringify(filters, null, 2)
      );
      console.log(
        "🔍 [DEBUG] Enhanced listConciergeOrderItemsWithRelationships called with options:",
        JSON.stringify(options, null, 2)
      );

      // Get concierge order items first
      const startTime = Date.now();
      console.log("🔍 [SQL] About to execute listConciergeOrderItems query...");

      const items = await this.listConciergeOrderItems(filters, {
        take: limit,
        skip: offset,
        ...listOptions,
      });

      const queryTime = Date.now() - startTime;
      console.log(
        `✅ [SQL] listConciergeOrderItems query completed in ${queryTime}ms`
      );

      console.log(
        "✅ [DEBUG] listConciergeOrderItems returned:",
        items.length,
        "items"
      );

      // Get total count for pagination
      const totalCount = await this.listConciergeOrderItems(filters, {
        take: undefined,
        skip: undefined,
      });

      console.log(
        "✅ [DEBUG] Total count query returned:",
        totalCount.length,
        "items"
      );

      if (!items || items.length === 0) {
        return {
          concierge_order_items: [],
          count: 0,
          limit,
          offset,
        };
      }

      // Create category lookup map from passed categories
      const categoryLookupMap =
        this.createCategoryLookupMapFromData(categories);
      console.log(
        `🔍 [DEBUG] Category lookup map created with ${
          Object.keys(categoryLookupMap).length
        } categories`
      );

      // Get services for fetching enhanced relationship data using proper Medusa patterns
      let orderService: any;
      let productCategoryService: any;
      let supplierProductsServicesService: any;

      try {
        orderService = this.container_["orderService"];
        console.log("✅ [DEBUG] Order service resolved successfully");
      } catch (error) {
        console.warn("Could not resolve order service:", error.message);
      }

      try {
        productCategoryService = this.container_["productCategoryService"];
        console.log(
          "✅ [DEBUG] Product category service resolved successfully"
        );
      } catch (error) {
        console.warn(
          "Could not resolve product category service:",
          error.message
        );
      }

      try {
        supplierProductsServicesService =
          this.container_["supplierProductsServicesService"];
        console.log(
          "✅ [DEBUG] Supplier products services service resolved successfully"
        );
      } catch (error) {
        console.warn(
          "Could not resolve supplier products services service:",
          error.message
        );
      }

      // Get enhanced relationship data for each item
      const enhancedItems = await Promise.all(
        items.map(async (item: any) => {
          try {
            console.log(
              `🔍 [DEBUG] Processing enhanced item ${item.id} with concierge_order_id: ${item.concierge_order_id}`
            );

            // Get concierge order data with enhanced relationships
            const conciergeOrder = await this.retrieveConciergeOrder(
              item.concierge_order_id
            );
            console.log(
              `✅ [DEBUG] Retrieved concierge order:`,
              conciergeOrder
                ? `ID: ${conciergeOrder.id}, order_id: ${conciergeOrder.order_id}`
                : "null"
            );

            // Initialize enhanced result object with required schema mapping
            const result: any = {
              // 1. request_id: concierge_order_item.id
              request_id: item.id,

              // Core item data
              ...item,

              // 4. title: concierge_order_item.title
              title: item.title,

              // 5. quantity: concierge_order_item.quantity
              quantity: item.quantity,

              // 6. unit_price: concierge_order_item.unit_price
              unit_price: item.unit_price,

              // 7. total_price: Calculate as quantity * unit_price
              total_price: item.quantity * item.unit_price,

              // 8. booking: concierge_order_item.concierge_order_id
              booking: item.concierge_order_id,

              // Format dates for display
              requested_date: item.created_at,
            };

            // Get category data if category_id exists - using pre-fetched lookup map
            if (item.category_id) {
              try {
                console.log(
                  `🔍 [DEBUG] Looking up category_id: ${item.category_id}`
                );

                if (categoryLookupMap && categoryLookupMap[item.category_id]) {
                  const categoryData = categoryLookupMap[item.category_id];
                  result.category = {
                    id: categoryData.id,
                    name: categoryData.name,
                    description: categoryData.description || null,
                    icon: categoryData.icon || null,
                  };
                  console.log(
                    `✅ [DEBUG] Retrieved category via pre-fetched lookup map:`,
                    result.category
                  );
                } else {
                  console.log(
                    `⚠️ [DEBUG] No category found in lookup map for category_id: ${item.category_id}`
                  );
                  console.log(
                    `🔍 [DEBUG] Available category IDs:`,
                    Object.keys(categoryLookupMap)
                  );
                }
              } catch (categoryError) {
                console.warn(
                  `❌ [DEBUG] Category lookup failed for item ${item.id}:`,
                  categoryError.message
                );
              }
            }

            console.log(
              `🔍 [DEBUG] Enhanced base result object for item ${item.id}:`,
              JSON.stringify(result, null, 2)
            );

            // Add enhanced concierge order data with hotel and date information
            if (conciergeOrder) {
              result.concierge_order = {
                id: conciergeOrder.id,
                order_id: conciergeOrder.order_id,
                assigned_to: conciergeOrder.assigned_to,
                notes: conciergeOrder.notes,
                status: conciergeOrder.status,
                last_contacted_at: conciergeOrder.last_contacted_at,
                created_at: conciergeOrder.created_at,
                updated_at: conciergeOrder.updated_at,
                metadata: conciergeOrder.metadata,
              };

              // 2. hotel: concierge_order.hotel_id → hotel.name
              if (conciergeOrder.hotel_id) {
                // For now, use hotel_id as fallback until proper hotel service is available
                result.hotel = conciergeOrder.hotel_id;
                console.log(`✅ [DEBUG] Hotel ID set: ${result.hotel}`);
              }

              // 10. check_in_date: concierge_order.check_in_date
              result.check_in_date = conciergeOrder.check_in_date;

              // 11. check_out_date: concierge_order.check_out_date
              result.check_out_date = conciergeOrder.check_out_date;

              // Legacy travel dates support
              if (conciergeOrder.metadata) {
                const metadata = conciergeOrder.metadata;
                result.travel_dates = {
                  check_in_date:
                    conciergeOrder.check_in_date ||
                    metadata.check_in_date ||
                    null,
                  check_out_date:
                    conciergeOrder.check_out_date ||
                    metadata.check_out_date ||
                    null,
                };
              }
            }

            // Get enhanced order and customer data if available
            if (conciergeOrder?.order_id && orderService) {
              try {
                console.log(
                  `🔍 [DEBUG] Fetching enhanced order data for order_id: ${conciergeOrder.order_id}`
                );

                // Fetch order data with customer relationships using order service
                const orderStartTime = Date.now();
                console.log(
                  `🔍 [SQL] About to execute orderService.retrieve with enhanced relations: ["customer", "items", "items.variant", "items.variant.product", "items.variant.product.categories"]`
                );

                const order = await orderService.retrieve(
                  conciergeOrder.order_id,
                  {
                    relations: [
                      "customer",
                      "items",
                      "items.variant",
                      "items.variant.product",
                      "items.variant.product.categories",
                    ],
                  }
                );

                const orderQueryTime = Date.now() - orderStartTime;
                console.log(
                  `✅ [SQL] orderService.retrieve completed in ${orderQueryTime}ms`
                );
                console.log(
                  `✅ [DEBUG] Enhanced order service returned:`,
                  order
                    ? `Order ID: ${order.id}, customer: ${
                        order.customer?.id
                      }, items count: ${order.items?.length || 0}`
                    : "null"
                );

                if (order) {
                  result.order = {
                    id: order.id,
                    display_id: order.display_id,
                    email: order.email,
                    currency_code: order.currency_code,
                    total: order.total,
                    created_at: order.created_at,
                    updated_at: order.updated_at,
                  };

                  // 9. customer: concierge_order.order_id → order.customer_id → customer.first_name + ' ' + customer.last_name
                  if (order.customer) {
                    const firstName = order.customer.first_name || "";
                    const lastName = order.customer.last_name || "";
                    const fullName = `${firstName} ${lastName}`.trim();

                    // Use full name if available, otherwise use email, otherwise use customer ID
                    result.customer =
                      fullName ||
                      order.customer.email ||
                      `Customer ${order.customer.id}`;

                    console.log(
                      `✅ [DEBUG] Customer data extracted: ${result.customer} (ID: ${order.customer.id}, firstName: ${firstName}, lastName: ${lastName})`
                    );
                  } else if (order.customer_id) {
                    // If customer object is not loaded but customer_id exists, try to fetch customer directly
                    try {
                      const customerService =
                        this.container_.resolve("customerService");
                      const customer = await customerService.retrieve(
                        order.customer_id
                      );
                      if (customer) {
                        const firstName = customer.first_name || "";
                        const lastName = customer.last_name || "";
                        const fullName = `${firstName} ${lastName}`.trim();
                        result.customer =
                          fullName ||
                          customer.email ||
                          `Customer ${customer.id}`;
                        console.log(
                          `✅ [DEBUG] Customer data fetched directly: ${result.customer} (ID: ${customer.id})`
                        );
                      } else {
                        result.customer =
                          order.email || `Customer ${order.customer_id}`;
                        console.log(
                          `⚠️ [DEBUG] Customer not found, using fallback: ${result.customer}`
                        );
                      }
                    } catch (customerError) {
                      console.warn(
                        `Failed to fetch customer ${order.customer_id}:`,
                        customerError.message
                      );
                      result.customer =
                        order.email || `Customer ${order.customer_id}`;
                      console.log(
                        `⚠️ [DEBUG] Customer fetch failed, using fallback: ${result.customer}`
                      );
                    }
                  } else {
                    result.customer = order.email || "Unknown Customer";
                    console.log(
                      `⚠️ [DEBUG] No customer or customer_id found, using order email: ${result.customer}`
                    );
                  }

                  // Find matching line item if line_item_id exists
                  if (item.line_item_id && order.items) {
                    console.log(
                      `🔍 [DEBUG] Looking for line_item_id: ${item.line_item_id} in ${order.items.length} order items`
                    );
                    console.log(
                      `🔍 [DEBUG] Order items IDs:`,
                      order.items.map((li: any) => li.id)
                    );

                    const lineItem = order.items.find(
                      (li: any) => li.id === item.line_item_id
                    );
                    console.log(
                      `✅ [DEBUG] Line item found:`,
                      lineItem
                        ? `ID: ${lineItem.id}, title: ${lineItem.title}`
                        : "null"
                    );

                    if (lineItem) {
                      result.order_line_item = {
                        id: lineItem.id,
                        unit_price: lineItem.unit_price,
                        quantity: lineItem.quantity,
                        title: lineItem.title,
                        variant_id: lineItem.variant_id,
                        product_id: lineItem.variant?.product_id,
                        metadata: lineItem.metadata,
                      };

                      // Order item is the same as line item in this context
                      result.order_item = {
                        id: lineItem.id,
                        quantity: lineItem.quantity,
                        unit_price: lineItem.unit_price,
                        title: lineItem.title,
                        variant_id: lineItem.variant_id,
                        product_id: lineItem.variant?.product_id,
                        metadata: lineItem.metadata,
                      };

                      // Extract product and category data from the line item variant
                      if (lineItem.variant?.product) {
                        const product = lineItem.variant.product;
                        console.log(
                          `✅ [DEBUG] Product found:`,
                          `ID: ${product.id}, title: ${
                            product.title
                          }, categories: ${product.categories?.length || 0}`
                        );

                        result.product = {
                          id: product.id,
                          title: product.title,
                          description: product.description,
                          metadata: product.metadata,
                        };

                        // Extract category information - store as string for consistency
                        if (
                          product.categories &&
                          product.categories.length > 0
                        ) {
                          // Use the first category as primary category
                          const primaryCategory = product.categories[0];
                          console.log(
                            `✅ [DEBUG] Primary category found:`,
                            `ID: ${primaryCategory.id}, name: ${primaryCategory.name}`
                          );

                          // Only set category if not already set by direct lookup
                          if (!result.category) {
                            result.category = primaryCategory.name;
                            console.log(
                              `✅ [DEBUG] Category set from product relationship: ${result.category}`
                            );
                          }
                        } else {
                          console.log(
                            `⚠️ [DEBUG] No categories found for product ${product.id}`
                          );
                        }
                      } else {
                        console.log(
                          `⚠️ [DEBUG] No product found in line item variant for line_item_id: ${item.line_item_id}`
                        );
                      }
                    }
                  }
                }
              } catch (orderError) {
                console.warn(
                  `Failed to fetch order data for ${conciergeOrder.order_id}:`,
                  orderError.message
                );
              }
            }

            // 3. category: Enhanced category lookup using concierge_order_item.category_id → product_service_category.name
            if (item.category_id) {
              try {
                console.log(
                  `🔍 [DEBUG] Direct category lookup for category_id: ${item.category_id}`
                );

                // Try multiple approaches for category lookup
                let categoryFound = false;

                // Approach 1: Direct category lookup using productCategoryService
                if (productCategoryService && !categoryFound) {
                  try {
                    const category =
                      await productCategoryService.retrieveProductCategory(
                        item.category_id
                      );
                    if (category) {
                      result.category = category.name;
                      categoryFound = true;
                      console.log(
                        `✅ [DEBUG] Direct category lookup successful: ${result.category}`
                      );
                    }
                  } catch (err) {
                    console.log(
                      `⚠️ [DEBUG] productCategoryService.retrieveProductCategory failed:`,
                      err.message
                    );
                  }
                }

                // Approach 2: Try using supplierProductsServicesService to get category
                if (supplierProductsServicesService && !categoryFound) {
                  try {
                    const categoryData =
                      await supplierProductsServicesService.retrieveProductCategory(
                        item.category_id
                      );
                    if (categoryData) {
                      result.category = categoryData.name;
                      categoryFound = true;
                      console.log(
                        `✅ [DEBUG] Supplier service category lookup successful: ${result.category}`
                      );
                    }
                  } catch (err) {
                    console.log(
                      `⚠️ [DEBUG] supplierProductsServicesService.retrieveProductCategory failed:`,
                      err.message
                    );
                  }
                }

                if (!categoryFound) {
                  console.log(
                    `⚠️ [DEBUG] All category lookup approaches failed for category_id: ${item.category_id}`
                  );
                }
              } catch (directCategoryError) {
                console.warn(
                  `Failed to fetch direct category data for category_id ${item.category_id}:`,
                  directCategoryError.message
                );
              }
            }

            // Enhanced category lookup using product_variant_id from product_service table (fallback)
            if (
              !result.category &&
              item.variant_id &&
              supplierProductsServicesService
            ) {
              try {
                console.log(
                  `🔍 [DEBUG] Attempting enhanced category lookup for variant_id: ${item.variant_id}`
                );

                // Find product_service record that has this variant_id as product_variant_id
                const productServices =
                  await supplierProductsServicesService.listProductServices({
                    product_variant_id: item.variant_id,
                  });

                if (productServices && productServices.length > 0) {
                  const productService = productServices[0];
                  console.log(
                    `✅ [DEBUG] Found product_service ${productService.id} for variant_id: ${item.variant_id}`
                  );

                  // Get the product service with category relationship
                  const productServiceWithCategory =
                    await supplierProductsServicesService.retrieveProductService(
                      productService.id,
                      { relations: ["category"] }
                    );

                  if (productServiceWithCategory?.category) {
                    result.category = productServiceWithCategory.category.name;
                    console.log(
                      `✅ [DEBUG] Enhanced category lookup successful: ${result.category}`
                    );
                  } else {
                    console.log(
                      `⚠️ [DEBUG] Product service ${productService.id} has no category`
                    );
                  }
                } else {
                  console.log(
                    `⚠️ [DEBUG] No product_service found with product_variant_id: ${item.variant_id}`
                  );
                }
              } catch (enhancedCategoryError) {
                console.warn(
                  `Failed to fetch enhanced category data for variant_id ${item.variant_id}:`,
                  enhancedCategoryError.message
                );
              }
            }

            console.log(
              `✅ [DEBUG] Final enhanced result for item ${item.id}:`,
              JSON.stringify(result, null, 2)
            );
            return result;
          } catch (error) {
            console.warn(
              `❌ [DEBUG] Failed to enhance item ${item.id}:`,
              error.message
            );
            const fallbackResult = {
              ...item,
              total_price: item.quantity * item.unit_price,
              requested_date: item.created_at,
            };
            console.log(
              `⚠️ [DEBUG] Fallback result for item ${item.id}:`,
              JSON.stringify(fallbackResult, null, 2)
            );
            return fallbackResult;
          }
        })
      );

      console.log(`🎉 [DEBUG] Final API response structure:`, {
        concierge_order_items_count: enhancedItems.length,
        count: totalCount.length,
        limit,
        offset,
        sample_item: enhancedItems.length > 0 ? enhancedItems[0] : null,
      });

      const finalResult = {
        concierge_order_items: enhancedItems,
        count: totalCount.length,
        limit,
        offset,
      };

      console.log(
        `🚀 [DEBUG] Returning final result with ${enhancedItems.length} enhanced items`
      );
      return finalResult;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list concierge order items with relationships: ${error.message}`
      );
    }
  }

  /**
   * Update concierge order item status
   */
  async updateConciergeOrderItemStatus(
    id: string,
    status: ConciergeOrderItemStatus,
    updatedBy?: string
  ): Promise<ConciergeOrderItemResponse> {
    try {
      const updateData: UpdateConciergeOrderItemInput = {
        status,
        metadata: {
          status_updated_by: updatedBy,
          status_updated_at: new Date().toISOString(),
        },
      };

      // If completing, set finalized_at and finalized_by
      if (status === ConciergeOrderItemStatus.COMPLETED) {
        updateData.finalized_at = new Date().toISOString();
        updateData.finalized_by = updatedBy;
      }

      return await this.updateConciergeOrderItem(id, updateData);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update concierge order item status: ${error.message}`
      );
    }
  }

  /**
   * Deactivate a concierge order item (soft removal)
   */
  async deactivateConciergeOrderItem(
    id: string,
    deactivatedBy?: string
  ): Promise<ConciergeOrderItemResponse> {
    try {
      return await this.updateConciergeOrderItem(id, {
        is_active: false,
        metadata: {
          deactivated_by: deactivatedBy,
          deactivated_at: new Date().toISOString(),
        },
      });
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to deactivate concierge order item: ${error.message}`
      );
    }
  }

  /**
   * Delete a concierge order item (soft delete)
   */
  async deleteConciergeOrderItem(id: string): Promise<void> {
    try {
      await this.softDeleteConciergeOrderItems([id]);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete concierge order item: ${error.message}`
      );
    }
  }
}

export default ConciergeManagementService;
