import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useState, useEffect } from "react";
import {
  Heading,
  Text,
  Button,
  Toaster,
  toast,
  Badge,
  Table,
  Input,
  DropdownMenu,
  FocusModal,
  Prompt,
  IconButton,
} from "@camped-ai/ui";
import { <PERSON><PERSON><PERSON>,  Spinner } from "@camped-ai/icons";
import {
  Bed,
  Edit,
  MoreHorizontal,
  Clock,
  XCircle,
  Upload,
  Trash2,
  ArrowLeft,
} from "lucide-react";
import BulkImportModal from "../../../../../../../components/room/bulk-import-modal";
import SimpleIndividualRoomForm from "../../../../../../../components/simple-individual-room-form";
import PermissionBasedSidebarHider from "../../../../../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../../../../../hooks/use-rbac";

interface Room {
  id: string;
  name: string;
  room_number: string;
  status: string;
  floor: string;
  notes: string;
  is_active: boolean;
  room_config_id: string;
  hotel_id: string;
  options: Record<string, string>;
  inventory_quantity: number;
  created_at?: string;
  updated_at?: string;
}

interface RoomConfig {
  id: string;
  name: string;
  type: string;
  description?: string;
  room_size?: string;
  amenities?: string[];
  bed_type?: string;
  max_extra_beds: number;
  max_adults: number;
  max_children: number;
  max_infants: number;
  max_occupancy: number;
  hotel_id: string;
}

const PageClient = () => {
  const { slug, id } = useParams();
  const navigate = useNavigate();
  const { hasPermission } = useRbac();
  const [hotel, setHotel] = useState<any>(null);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [roomConfig, setRoomConfig] = useState<RoomConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [open, setOpen] = useState(false);
  const [editMode, setEditMode] = useState(false);
  const [editId, setEditId] = useState("");
  const [importModalOpen, setImportModalOpen] = useState(false);
  const [submitForm, setSubmitForm] = useState<(() => void) | null>(null);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [deletePromptOpen, setDeletePromptOpen] = useState(false);
  const [roomToDelete, setRoomToDelete] = useState<Room | null>(null);
  const [isDeleting, setIsDeleting] = useState(false);
  // Room status is now managed through the availability calendar only

  useEffect(() => {
    fetchHotelAndRooms();
  }, [slug, id]);

  const fetchHotelAndRooms = async () => {
    setIsLoading(true);
    try {
      console.log("Fetching hotel details for slug:", slug);
      // Fetch hotel details
      const hotelResponse = await fetch(
        `/admin/hotel-management/hotels/${slug}`,
        {
          credentials: "include",
        }
      );
      console.log("Hotel response status:", hotelResponse.status);
      const hotelData = await hotelResponse.json();
      console.log("Hotel data:", hotelData);

      // Use the slug as the hotel ID if the hotel data doesn't have an ID
      const hotelId = hotelData.hotel?.id || slug;
      console.log("Using hotel ID:", hotelId);

      // Set the hotel data with a fallback for the ID
      setHotel({
        ...hotelData.hotel,
        id: hotelId,
      });

      // Fetch room configuration details
      console.log("Fetching room config details for ID:", id);
      const roomConfigResponse = await fetch(
        `/admin/direct-room-configs?room_config_id=${id}`,
        {
          credentials: "include",
        }
      );
      const roomConfigData = await roomConfigResponse.json();
      console.log("Room config data:", roomConfigData);

      if (roomConfigData.roomConfigs && roomConfigData.roomConfigs.length > 0) {
        setRoomConfig(roomConfigData.roomConfigs[0]);
      } else {
        console.error("Room configuration not found");
        toast.error("Error", {
          description: "Room configuration not found",
        });
      }

      // Fetch all rooms for this room configuration
      console.log("Fetching rooms for room config ID:", id);
      const roomsResponse = await fetch(
        `/admin/direct-rooms?room_config_id=${id}`,
        {
          credentials: "include",
          headers: {
            "Cache-Control": "no-cache, no-store, must-revalidate",
            Pragma: "no-cache",
            Expires: "0",
          },
        }
      );
      const roomsData = await roomsResponse.json();
      console.log("Rooms data:", roomsData);

      if (roomsData.rooms && Array.isArray(roomsData.rooms)) {
        setRooms(roomsData.rooms);
      } else {
        setRooms([]);
      }
    } catch (error) {
      console.error("Error fetching data:", error);
      toast.error("Error", {
        description: "Failed to load hotel and rooms",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Room status is now managed through the availability calendar only

  const handleDeleteRoom = (room: Room) => {
    setRoomToDelete(room);
    setDeletePromptOpen(true);
  };

  const executeDeleteRoom = async () => {
    if (!roomToDelete) return;

    setIsDeleting(true);
    try {
      const response = await fetch(`/admin/direct-rooms`, {
        method: "DELETE",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ ids: [roomToDelete.id] }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete room");
      }

      // Remove the deleted room from the state
      setRooms(rooms.filter((room) => room.id !== roomToDelete.id));

      // Close prompt and reset state
      setDeletePromptOpen(false);
      setRoomToDelete(null);

      toast.success("Success", {
        description: "Room deleted successfully",
      });
    } catch (error) {
      console.error("Error deleting room:", error);
      toast.error("Error", {
        description:
          error instanceof Error ? error.message : "Failed to delete room",
      });
    } finally {
      setIsDeleting(false);
    }
  };

  // Status is now managed through the availability calendar only

  const filteredRooms = rooms.filter((room) => {
    const searchLower = searchTerm.toLowerCase();
    return (
      room.name.toLowerCase().includes(searchLower) ||
      room.room_number.toLowerCase().includes(searchLower) ||
      room.floor.toLowerCase().includes(searchLower) ||
      room.notes.toLowerCase().includes(searchLower)
    );
  });

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <div className="flex flex-col ">
        {/* Back Navigation - Outside Header */}
        <div className=" pb-4 flex flex-row items-center justify-between">
          <div className="flex items-center gap-4">
            <IconButton
              onClick={() =>
                navigate(`/hotel-management/hotels/${slug}/room-configs`)
              }
            >
              <ArrowLeft className="w-4 h-4" />
            </IconButton>
            <Heading
              level="h1"
              className="text-xl text-gray-900 dark:text-gray-100"
            >
              {roomConfig?.name || "Room Configuration"}
            </Heading>
          </div>
          {/* Search and Action Buttons Row */}
          <div className="flex flex-row">
            <Input
              id="search-rooms"
              placeholder="Search by room number, name, floor..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full lg:w-80 rounded-lg border-gray-300 dark:border-gray-600 mr-2"
            />

            {/* Action Buttons */}
            <div className="flex flex-wrap gap-2 lg:flex-nowrap">
              {searchTerm && (
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => setSearchTerm("")}
                  className="h-10 flex items-center"
                >
                  <XCircle className="w-4 h-4 mr-2" />
                  Clear
                </Button>
              )}
              {hasPermission("rooms:availability") && (
                <Button
                  variant="secondary"
                  onClick={() =>
                    navigate(
                      `/hotel-management/hotels/${slug}/availability-new?roomTypes=${id}`
                    )
                  }
                >
                  <Clock className="w-4 h-4 mr-2" />
                  Manage Availability
                </Button>
              )}
              {hasPermission("rooms:bulk_import") && (
                <Button
                  variant="secondary"
                  size="small"
                  onClick={() => setImportModalOpen(true)}
                >
                  <Upload className="w-4 h-4 mr-2" />
                  Import
                </Button>
              )}
              {hasPermission("rooms:create") && (
                <Button
                  variant="primary"
                  size="small"
                  onClick={() => {
                    setEditMode(false);
                    setEditId("");
                    setOpen(true);
                  }}
                >
                  <PlusMini className="w-4 h-4 mr-2" />
                  Add Room
                </Button>
              )}
            </div>
          </div>
        </div>

        {/* Main Content */}

        {/* Rooms Table */}
        <div className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden">
          <div className="border-b border-gray-200 dark:border-gray-700 px-5 py-4">
            <Heading
              level="h2"
              className="text-lg font-semibold dark:text-gray-200"
            >
              {filteredRooms.length > 0
                ? `Rooms (${filteredRooms.length})`
                : "Rooms"}
            </Heading>
          </div>

          {isLoading ? (
            <div className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
                <div className="h-10 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
              </div>
            </div>
          ) : filteredRooms.length === 0 ? (
            <div className="p-8 text-center">
              <div className="flex flex-col items-center justify-center py-8">
                <div className="bg-blue-50 dark:bg-blue-900/30 p-4 rounded-full mb-4">
                  <Bed className="w-8 h-8 text-blue-600 dark:text-blue-400" />
                </div>
                <Heading
                  level="h3"
                  className="text-xl font-semibold mb-2 dark:text-gray-200"
                >
                  {searchTerm ? "No matching rooms" : "No rooms found"}
                </Heading>
                <Text className="text-gray-500 dark:text-gray-400 mb-6 max-w-md">
                  {searchTerm
                    ? "We couldn't find any rooms matching your search criteria."
                    : "There are no rooms created for this room configuration yet."}
                </Text>
                {!searchTerm && hasPermission("rooms:create") && (
                  <Button
                    variant="primary"
                    onClick={() => {
                      setEditMode(false);
                      setEditId("");
                      setOpen(true);
                    }}
                  >
                    <PlusMini className="w-4 h-4 mr-2" />
                    Add Your First Room
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <div>
              <Table>
                <Table.Header>
                  <Table.Row>
                    <Table.HeaderCell>Room Number</Table.HeaderCell>
                    <Table.HeaderCell>Floor</Table.HeaderCell>
                    <Table.HeaderCell>Notes</Table.HeaderCell>
                    <Table.HeaderCell>Actions</Table.HeaderCell>
                  </Table.Row>
                </Table.Header>
                <Table.Body>
                  {filteredRooms.map((room) => (
                    <Table.Row key={room.id}>
                      <Table.Cell>
                        <div className="flex items-center gap-2">
                          <div className="bg-blue-50 dark:bg-blue-900/30 p-1 rounded">
                            <Bed className="w-4 h-4 text-blue-600 dark:text-blue-400" />
                          </div>
                          <div>
                            <Text className="font-medium dark:text-gray-200">
                              {room.room_number}
                            </Text>
                            <Text className="text-xs text-gray-500 dark:text-gray-400">
                              {room.name}
                            </Text>
                          </div>
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        <Badge color="grey" className="font-normal">
                          Floor {room.floor || "N/A"}
                        </Badge>
                      </Table.Cell>
                      <Table.Cell>
                        <Text className="truncate max-w-[200px] dark:text-gray-300">
                          {room.notes || "-"}
                        </Text>
                      </Table.Cell>
                      <Table.Cell>
                        <DropdownMenu>
                          <DropdownMenu.Trigger asChild>
                            <Button
                              variant="secondary"
                              size="small"
                              className="h-8 w-8 p-0"
                            >
                              <MoreHorizontal className="w-4 h-4" />
                            </Button>
                          </DropdownMenu.Trigger>
                          <DropdownMenu.Content align="end">
                            {hasPermission("rooms:edit") && (
                              <DropdownMenu.Item
                                onClick={() => {
                                  // Open edit modal
                                  setEditMode(true);
                                  setEditId(room.id);
                                  setOpen(true);
                                }}
                              >
                                <Edit className="w-4 h-4 mr-2" />
                                Edit Room
                              </DropdownMenu.Item>
                            )}
                            {hasPermission("rooms:edit") &&
                              hasPermission("rooms:availability") && (
                                <DropdownMenu.Separator />
                              )}
                            {hasPermission("rooms:availability") && (
                              <DropdownMenu.Item
                                onClick={() => {
                                  // Navigate to availability page
                                  navigate(
                                    `/hotel-management/hotels/${slug}/availability-new?rooms=${room.id}`
                                  );
                                }}
                              >
                                <Clock className="w-4 h-4 mr-2" />
                                Manage Availability
                              </DropdownMenu.Item>
                            )}
                            {(hasPermission("rooms:edit") ||
                              hasPermission("rooms:availability")) &&
                              hasPermission("rooms:delete") && (
                                <DropdownMenu.Separator />
                              )}
                            {hasPermission("rooms:delete") && (
                              <DropdownMenu.Item
                                onClick={() => {
                                  handleDeleteRoom(room);
                                }}
                                className="text-red-600 focus:text-red-600"
                              >
                                <Trash2 className="w-4 h-4 mr-2" />
                                Delete Room
                              </DropdownMenu.Item>
                            )}
                          </DropdownMenu.Content>
                        </DropdownMenu>
                      </Table.Cell>
                    </Table.Row>
                  ))}
                </Table.Body>
              </Table>
            </div>
          )}
        </div>
      </div>
      {/* Create/Edit Room Modal */}
      <FocusModal open={open} onOpenChange={setOpen}>
        <FocusModal.Content className="flex flex-col h-full max-h-[95vh] bg-white dark:bg-gray-900">
          <FocusModal.Header className="flex-shrink-0 bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
            <div className="flex justify-between items-center w-full py-4 px-6">
              <Heading
                level="h2"
                className="text-xl font-semibold text-gray-900 dark:text-gray-100"
              >
                {editMode ? "Edit Room" : "Add New Room"}
              </Heading>
            </div>
          </FocusModal.Header>
          <FocusModal.Body className="flex-1 overflow-y-auto bg-gray-50 dark:bg-gray-900">
            <SimpleIndividualRoomForm
              hotelId={hotel?.id || slug}
              roomConfigId={id}
              initialData={
                editMode ? rooms.find((room) => room.id === editId) : undefined
              }
              isEdit={editMode}
              renderFooterButtons={false}
              onSubmit={(submitFn) => setSubmitForm(() => submitFn)}
              onSubmittingChange={setIsSubmitting}
              onComplete={(success: boolean) => {
                setOpen(false);
                if (success) {
                  fetchHotelAndRooms();
                  toast.success(editMode ? "Room Updated" : "Room Created", {
                    description: editMode
                      ? "The room has been updated successfully."
                      : "A new room has been created successfully.",
                  });
                }
              }}
            />
          </FocusModal.Body>
          <div className="border-t border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 px-6 py-4 sticky bottom-0 z-10">
            <div className="flex justify-end">
              <Button
                variant="secondary"
                size="small"
                onClick={() => setOpen(false)}
                className="h-9 flex items-center mr-2"
              >
                <XCircle className="w-4 h-4 mr-2" />
                Cancel
              </Button>
              <Button
                variant="primary"
                size="small"
                onClick={() => submitForm && submitForm()}
                disabled={!submitForm}
                className="h-9 flex items-center"
              >
                {isSubmitting ? (
                  <>
                    <Spinner className="w-4 h-4 mr-2 animate-spin" />
                    Saving...
                  </>
                ) : editMode ? (
                  <>
                    <Edit className="w-4 h-4 mr-2" />
                    Update Room
                  </>
                ) : (
                  <>
                    <PlusMini className="w-4 h-4 mr-2" />
                    Create Room
                  </>
                )}
              </Button>
            </div>
          </div>
        </FocusModal.Content>
      </FocusModal>

      {/* Bulk Import Modal */}
      <BulkImportModal
        open={importModalOpen}
        onClose={() => {
          setImportModalOpen(false);
          // Refresh data when modal closes with a slight delay to ensure server has processed everything
          setTimeout(() => {
            console.log("Refreshing rooms after import");
            fetchHotelAndRooms();
          }, 500);
        }}
        onDataRefresh={() => {
          console.log("Refreshing rooms after successful import");
          fetchHotelAndRooms();
        }}
        hotelId={slug}
        roomConfigId={id}
      />

      {/* Delete Confirmation Prompt */}
      <Prompt open={deletePromptOpen} onOpenChange={setDeletePromptOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Room</Prompt.Title>
            <Prompt.Description>
              Are you sure you want to delete room "{roomToDelete?.room_number}
              "? This action cannot be undone.
            </Prompt.Description>
          </Prompt.Header>
          <Prompt.Footer>
            <Prompt.Cancel
              onClick={() => {
                setDeletePromptOpen(false);
                setRoomToDelete(null);
              }}
              disabled={isDeleting}
            >
              Cancel
            </Prompt.Cancel>
            <Prompt.Action onClick={executeDeleteRoom} disabled={isDeleting}>
              {isDeleting ? (
                <>
                  <Spinner className="w-3 h-3 mr-1 animate-spin" />
                  Deleting...
                </>
              ) : (
                "Delete"
              )}
            </Prompt.Action>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </>
  );
};



export default PageClient;
