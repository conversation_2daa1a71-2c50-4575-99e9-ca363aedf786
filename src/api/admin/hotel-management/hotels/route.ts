import {
  AuthenticatedMedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import {
  PostAdminCreateHotel,
  PostAdminDeleteHotel,
  PostAdminUpdateHotel,
} from "./validators";
import { CreateHotelWorkflow } from "src/workflows/hotel-management/hotel/create-hotel";
import { UpdateHotelWorkflow } from "src/workflows/hotel-management/hotel/update-hotel";
import { DeleteHotelWorkflow } from "src/workflows/hotel-management/hotel/delete-hotel";
import { ContainerRegistrationKeys, Modules } from "@camped-ai/framework/utils";
import { RBAC_MODULE } from "../../../../modules/rbac";
import RbacModuleService from "../../../../modules/rbac/service";
import { UserRole } from "../../../../modules/rbac/types";

type PostAdminCreateHotelType = z.infer<typeof PostAdminCreateHotel>;
type PostAdminDeleteHotelType = z.infer<typeof PostAdminDeleteHotel>;
type PostAdminUpdateHotelType = z.infer<typeof PostAdminUpdateHotel>;

export const POST = async (
  req: AuthenticatedMedusaRequest<PostAdminCreateHotelType>,
  res: MedusaResponse
) => {
  try {
    // Check permission to create hotels
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    if (req.auth_context?.actor_id) {
      const user = await userService.retrieveUser(req.auth_context.actor_id);
      const userWithRole = {
        ...user,
        created_at: user.created_at.toISOString(),
        updated_at: user.updated_at.toISOString(),
      };
      const hasPermission = await rbacService.hasPermission(
        userWithRole,
        "hotel_management:create" as any
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required_permission: "hotel_management:create",
        });
      }
    } else {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { result } = await CreateHotelWorkflow(req.scope).run({
      //@ts-ignore
      input: req.body,
    });
    res.json({ hotel: result });
  } catch (error) {
    console.error("Error creating hotel:", error);
    res.status(500).json({ error: "Failed to create hotel" });
  }
};

export const PUT = async (
  req: AuthenticatedMedusaRequest<PostAdminUpdateHotelType>,
  res: MedusaResponse
) => {
  try {
    // Check permission to edit hotels
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    if (req.auth_context?.actor_id) {
      const user = await userService.retrieveUser(req.auth_context.actor_id);
      const userWithRole = {
        ...user,
        created_at: user.created_at.toISOString(),
        updated_at: user.updated_at.toISOString(),
      };
      const hasPermission = await rbacService.hasPermission(
        userWithRole,
        "hotel_management:edit" as any
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required_permission: "hotel_management:edit",
        });
      }
    } else {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { result } = await UpdateHotelWorkflow(req.scope).run({
      //@ts-ignore
      input: req.body,
    });
    res.json({ hotel: result });
  } catch (error) {
    console.error("Error updating hotel:", error);
    res.status(500).json({ error: "Failed to update hotel" });
  }
};
export const DELETE = async (
  req: AuthenticatedMedusaRequest<PostAdminDeleteHotelType>,
  res: MedusaResponse
) => {
  try {
    // Check permission to delete hotels
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    if (req.auth_context?.actor_id) {
      const user = await userService.retrieveUser(req.auth_context.actor_id);
      const userWithRole = {
        ...user,
        created_at: user.created_at.toISOString(),
        updated_at: user.updated_at.toISOString(),
      };
      const hasPermission = await rbacService.hasPermission(
        userWithRole,
        "hotel_management:delete" as any
      );

      if (!hasPermission) {
        return res.status(403).json({
          error: "Insufficient permissions",
          required_permission: "hotel_management:delete",
        });
      }
    } else {
      return res.status(401).json({ error: "Authentication required" });
    }

    const { result } = await DeleteHotelWorkflow(req.scope).run({
      //@ts-ignore
      input: req.body,
    });

    res.json({ hotel: result });
  } catch (error) {
    console.error("Error deleting hotel:", error);
    res.status(500).json({ error: "Failed to delete hotel" });
  }
};

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const rbacService: RbacModuleService = req.scope.resolve(RBAC_MODULE);
    const userService = req.scope.resolve(Modules.USER);

    // Check permission to view hotels
    let userWithRole = null;
    if (req.auth_context?.actor_id) {
      try {
        userWithRole = await userService.retrieveUser(
          req.auth_context.actor_id
        );

        // Check if user has permission to view hotels
        // Allow both hotel_management:view and bookings:view since bookings need hotel data
        const hasHotelPermission = await rbacService.hasPermission(
          userWithRole,
          "hotel_management:view" as any
        );
        const hasBookingPermission = await rbacService.hasPermission(
          userWithRole,
          "bookings:view" as any
        );

        if (!hasHotelPermission && !hasBookingPermission) {
          return res.status(403).json({
            error: "Insufficient permissions",
            required_permission: "hotel_management:view or bookings:view",
          });
        }
      } catch (error) {
        console.warn("Could not load user RBAC data:", error);
        return res.status(403).json({ error: "Authentication required" });
      }
    }

    const { limit: rawLimit = 20, offset: rawOffset = 0, is_featured, is_active, search, destination_id, star_rating } = req.query || {};

    // Ensure limit and offset are properly converted to numbers
    const limit = parseInt(String(rawLimit), 10) || 20;
    const offset = parseInt(String(rawOffset), 10) || 0;
    const filters: Record<string, any> = {};

    // Add featured status filter at database level
    if (is_featured !== undefined) {
      // Convert string to boolean if needed
      const featuredValue = typeof is_featured === 'string' ? is_featured === 'true' : is_featured;
      filters.is_featured = featuredValue;
    }

    // Add active status filter at database level
    if (is_active !== undefined) {
      // Convert string to boolean if needed
      const activeValue = typeof is_active === 'string' ? is_active === 'true' : is_active;
      filters.is_active = activeValue;
    }

    // Add destination filter at database level
    if (destination_id && typeof destination_id === 'string' && destination_id.trim()) {
      filters.destination_id = destination_id.trim();
    }

    // Add star rating filter at database level
    if (star_rating) {
      if (typeof star_rating === 'string') {
        // Handle comma-separated star ratings (e.g., "3,4,5")
        const ratings = star_rating.split(',').map(r => parseInt(r.trim())).filter(r => !isNaN(r) && r >= 1 && r <= 5);

        if (ratings.length > 0) {
          // Create rating range filters for each selected star rating
          const ratingFilters = ratings.map(rating => ({
            $and: [
              { rating: { $gte: rating } },
              { rating: { $lt: rating + 1 } }
            ]
          }));

          if (ratingFilters.length === 1) {
            filters.$and = ratingFilters[0].$and;
          } else {
            filters.$or = ratingFilters;
          }
        }
      } else if (Array.isArray(star_rating)) {
        // Handle array of star ratings
        const ratings = star_rating.map(r => parseInt(String(r))).filter(r => !isNaN(r) && r >= 1 && r <= 5);

        if (ratings.length > 0) {
          const ratingFilters = ratings.map(rating => ({
            $and: [
              { rating: { $gte: rating } },
              { rating: { $lt: rating + 1 } }
            ]
          }));

          if (ratingFilters.length === 1) {
            filters.$and = ratingFilters[0].$and;
          } else {
            filters.$or = ratingFilters;
          }
        }
      }
    }

    // Handle search functionality
    let searchDestinationIds: string[] = [];
    if (search && typeof search === 'string' && search.trim()) {
      const searchTerm = search.trim();

      // First, find destinations that match the search term
      // Only search in active destinations to maintain data integrity
      try {
        const { data: matchingDestinations } = await query.graph({
          entity: "destination",
          filters: {
            name: { $ilike: `%${searchTerm}%` },
            is_active: true // Only search active destinations
          },
          fields: ["id"],
        });

        if (matchingDestinations && matchingDestinations.length > 0) {
          searchDestinationIds = matchingDestinations.map((dest: any) => dest.id);
        }
      } catch (error) {
        console.error("Error searching destinations:", error);
      }

      // Build search filters for hotels
      const searchFilters: any[] = [
        { name: { $ilike: `%${searchTerm}%` } },
        { description: { $ilike: `%${searchTerm}%` } },
        { address: { $ilike: `%${searchTerm}%` } }
      ];

      // If we found matching destinations, include hotels from those destinations
      // This ensures search results only include hotels with valid destination references
      if (searchDestinationIds.length > 0) {
        searchFilters.push({ destination_id: { $in: searchDestinationIds } });
      }

      // Combine with existing filters
      if (Object.keys(filters).length > 0) {
        filters.$and = [
          { ...filters },
          { $or: searchFilters }
        ];
        // Remove the individual filter properties to avoid conflicts
        Object.keys(filters).forEach(key => {
          if (key !== '$and') {
            delete filters[key];
          }
        });
      } else {
        filters.$or = searchFilters;
      }
    }

    // DESTINATION VALIDATION: Ensure data integrity by filtering out orphaned hotels
    // This prevents hotels with invalid destination_id references from appearing in the admin panel
    // Hotels that reference non-existent destinations will be excluded from the API response
    let validDestinationIds: string[] = [];
    try {
      const { data: allDestinations } = await query.graph({
        entity: "destination",
        fields: ["id", "name"],
        filters: { is_active: true }, // Only get active destinations
      });

      if (allDestinations && allDestinations.length > 0) {
        validDestinationIds = allDestinations.map((dest: any) => dest.id);
      }
    } catch (error) {
      console.error("Error fetching valid destinations:", error);
      // If we can't fetch destinations, return empty result to prevent showing orphaned hotels
      return res.json({
        hotels: [],
        count: 0,
        limit: Number(limit),
        offset: Number(offset),
      });
    }

    // Add destination validation to filters to exclude orphaned hotels
    const destinationValidationFilter = {
      destination_id: { $in: validDestinationIds }
    };

    // Combine destination validation with existing filters
    let finalFilters: any;
    if (Object.keys(filters).length > 0) {
      // If we already have filters, combine them with destination validation
      if (filters.$and) {
        // If there's already an $and clause, add destination validation to it
        finalFilters = {
          $and: [
            ...filters.$and,
            destinationValidationFilter
          ]
        };
      } else if (filters.$or) {
        // If there's an $or clause, wrap everything in $and with destination validation
        finalFilters = {
          $and: [
            { $or: filters.$or },
            destinationValidationFilter
          ]
        };
      } else {
        // Simple filters, combine with destination validation
        finalFilters = {
          $and: [
            filters,
            destinationValidationFilter
          ]
        };
      }
    } else {
      // No existing filters, just use destination validation
      finalFilters = destinationValidationFilter;
    }

    // Get hotels with valid destination references only
    const {
      data: allHotels,
      metadata: { count: totalCount, take, skip },
    } = await query.graph({
      entity: "hotel",
      fields: ["*", "roomConfigs.id", "roomConfigs.name", "images.*"],
      filters: finalFilters,
      pagination: {
        skip: offset,
        take: limit,
      },
    });

    // Filter hotels based on user permissions
    let filteredHotels = allHotels;
    let filteredCount = totalCount;

    if (userWithRole) {
      const rbacData = userWithRole.metadata?.rbac as any;

      // All users with valid roles can see all hotels
      // Access control is now permission-based, not hotel-based
      if (rbacData?.role !== UserRole.ADMIN) {
        // Non-admin users see all hotels but with permission-based operations
        filteredHotels = allHotels;
        filteredCount = allHotels.length;
      }
    }

    // Create destination map for adding names to hotels
    // Since we already fetched all destinations above, reuse that data
    let destinationMap = new Map<string, string>();
    try {
      const { data: destinations } = await query.graph({
        entity: "destination",
        fields: ["id", "name"],
        filters: { id: { $in: validDestinationIds } },
      });

      if (destinations && destinations.length > 0) {
        destinations.forEach((destination: { id: string; name: string }) => {
          destinationMap.set(destination.id, destination.name);
        });
      }
    } catch (error) {
      console.error("Error fetching destination names for mapping:", error);
    }

    // Add destination names to hotels (all hotels should have valid destinations now)
    const hotelsWithDestinations = filteredHotels.map((hotel: any) => ({
      ...hotel,
      destination_name: hotel.destination_id ? destinationMap.get(hotel.destination_id) : null,
    }));

    const response = {
      hotels: hotelsWithDestinations || [],
      count: filteredCount || 0,
      limit: take || Number(limit),
      offset: skip || Number(offset),
    };

    res.json(response);
  } catch (error) {
    console.error("Error in hotel listing:", error);
    res.status(500).json({
      type: "server_error",
      message: "Failed to list hotels",
    });
  }
};
