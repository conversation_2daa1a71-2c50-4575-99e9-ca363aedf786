import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { CONCIERGE_MANAGEMENT_MODULE } from "../../../../modules/concierge-management";
import { ConciergeOrderItemStatus } from "../../../../modules/concierge-management/types";
import { SUPPLIER_PRODUCTS_SERVICES_MODULE } from "../../../../modules/supplier-products-services";

// Validation schema for query parameters
export const GetAdminOnRequestItemsQuery = z.object({
  limit: z.coerce.number().min(1).max(100).optional().default(20),
  offset: z.coerce.number().min(0).optional().default(0),
  status: z.nativeEnum(ConciergeOrderItemStatus).optional(),
  exclude_completed: z.coerce.boolean().optional().default(true), // Default to exclude completed items
  is_active: z.coerce.boolean().optional(),
  line_item_id: z.string().optional(),
  variant_id: z.string().optional(),
  concierge_order_id: z.string().optional(),
  added_by: z.string().optional(),
  finalized_by: z.string().optional(),
  search: z.string().optional(),
  order: z.string().optional().default("created_at"),
  sort_order: z.enum(["asc", "desc"]).optional().default("desc"),
  created_at_gte: z.string().datetime().optional(),
  created_at_lte: z.string().datetime().optional(),
});

export type GetAdminOnRequestItemsQueryType = z.infer<typeof GetAdminOnRequestItemsQuery>;

/**
 * GET /admin/supplier-management/on-request
 * List all ConciergeOrderItem records with comprehensive relationship data
 *
 * This endpoint provides access to all concierge order items with related data including:
 * - Request ID (concierge_order_item.id)
 * - Category information from product relationships
 * - Title, quantity, unit price, and calculated total price
 * - Requested date (created_at) and travel dates from concierge order metadata
 * - Related order, line item, product, and category data
 *
 * Supports filtering, pagination, and multi-select actions for supplier management.
 * URL pattern: /admin/supplier-management/on-request?status=under_review&page=1
 */
export const GET = async (
  req: MedusaRequest<GetAdminOnRequestItemsQueryType>,
  res: MedusaResponse
) => {
  try {
    console.log("🔍 Fetching on-request items (ConciergeOrderItem)...");

    const conciergeManagementService = req.scope.resolve(CONCIERGE_MANAGEMENT_MODULE);

    // Validate and parse query parameters
    const {
      limit,
      offset,
      status,
      exclude_completed,
      is_active,
      line_item_id,
      variant_id,
      concierge_order_id,
      added_by,
      finalized_by,
      search,
      order: orderField,
      sort_order,
      created_at_gte,
      created_at_lte,
    } = GetAdminOnRequestItemsQuery.parse(req.query);

    // Build filters object
    const filters: any = {};

    if (status) {
      filters.status = status;
    } else if (exclude_completed) {
      // Default behavior: exclude completed items unless specifically requested
      filters.status = { $ne: "completed" };
    }

    if (is_active !== undefined) filters.is_active = is_active;
    if (line_item_id) filters.line_item_id = line_item_id;
    if (variant_id) filters.variant_id = variant_id;
    if (concierge_order_id) filters.concierge_order_id = concierge_order_id;
    if (added_by) filters.added_by = added_by;
    if (finalized_by) filters.finalized_by = finalized_by;

    // Handle date range filters
    if (created_at_gte || created_at_lte) {
      filters.created_at = {};
      if (created_at_gte) filters.created_at.gte = new Date(created_at_gte);
      if (created_at_lte) filters.created_at.lte = new Date(created_at_lte);
    }

    // Build options object
    const options = {
      limit,
      offset,
      order: { [orderField]: sort_order.toUpperCase() as "ASC" | "DESC" },
    };

    console.log("🔍 Query filters:", filters);
    console.log("🔍 Query options:", options);

    // Pre-fetch categories using supplier products services module
    let categories = [];
    try {
      const supplierProductsServicesService = req.scope.resolve(SUPPLIER_PRODUCTS_SERVICES_MODULE);
      categories = await supplierProductsServicesService.listCategories(
        { is_active: true },
        { skip: 0, take: 100 }
      );
      console.log(`✅ [API] Pre-fetched ${categories.length} categories for lookup`);
    } catch (error) {
      console.warn(`⚠️ [API] Failed to pre-fetch categories:`, error.message);
    }

    // Fetch data using the concierge management service with relationship data
    const result = await conciergeManagementService.listConciergeOrderItemsWithRelationships(filters, options, categories);

    console.log(`✅ Found ${result.count} on-request items`);

    return res.json({
      concierge_order_items: result.concierge_order_items,
      count: result.count,
      limit: result.limit,
      offset: result.offset,
    });

  } catch (error) {
    console.error("❌ Error fetching on-request items:", error);
    
    if (error instanceof z.ZodError) {
      return res.status(400).json({ 
        message: "Validation error", 
        errors: error.errors 
      });
    }

    return res.status(500).json({
      message: "Internal server error",
      error: error.message,
    });
  }
};
