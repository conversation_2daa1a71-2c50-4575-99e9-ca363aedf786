import { useParams, useNavigate } from "react-router-dom";
import { useState, useMemo } from "react";
import { Container, Heading, Text, Button, Toaster } from "@camped-ai/ui";
import { ArrowLeft, Tags } from "lucide-react";
import PricingCalendarView from "../../../../../components/hotel/pricing/pricing-calendar-view";
import { useAdminHotelComprehensivePricing } from "../../../../../hooks/hotel/use-admin-hotel-comprehensive-pricing";
import { useRbac } from "../../../../../hooks/use-rbac";
import { useHotels } from "../../../../../hooks/supplier-products-services/use-hotels";
import PermissionBasedSidebarHider from "../../../../../widgets/permission-based-sidebar-hider";

const PageClient = () => {
  const { slug } = useParams();
  const navigate = useNavigate();
  const { hasPermission, loading: rbacLoading } = useRbac();
  const [currentCurrency] = useState("GBP"); // Default currency

  // Use the hotels hook to get hotel details
  const { data: hotelsData, isLoading: isLoadingHotels } = useHotels({
    limit: 100,
    is_active: true,
  });

  // Find the hotel by slug
  const hotel = useMemo(() => {
    if (!hotelsData?.hotels || !slug) return null;
    return hotelsData.hotels.find((h: any) => h.id === slug || h.slug === slug);
  }, [hotelsData?.hotels, slug]);

  // Use the comprehensive pricing hook for pricing data with currency
  const {
    data: comprehensiveData,
    isLoading: isLoadingPricing,
    isError,
  } = useAdminHotelComprehensivePricing(hotel?.id || "", currentCurrency);





  const isLoading = isLoadingHotels || isLoadingPricing;

  if (isLoading) {
    return (
      <div className="h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Check permissions first - wait for RBAC loading to complete
  if (!rbacLoading && !hasPermission("pricing:view")) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">
            You don't have permission to view pricing information
          </Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/hotel-management/hotels")}
          >
            Back to Hotels
          </Button>
        </div>
      </Container>
    );
  }

  if (!hotel) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">Hotel not found</Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate("/hotel-management/hotels")}
          >
            Back to Hotels
          </Button>
        </div>
      </Container>
    );
  }

  // Handle error state
  if (isError || !hotel) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">
            Failed to load hotel pricing data
          </Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Hotel
          </Button>
        </div>
      </Container>
    );
  }

  // Transform comprehensive data for calendar component
  const transformedData = useMemo(() => {
    if (!comprehensiveData) return { basePriceRules: [], seasonalRules: [] };

    const basePriceRules: any[] = [];
    const seasonalRules: any[] = [];

    // Extract base price rules from room pricing data
    comprehensiveData.room_pricing_data?.forEach((roomData: any) => {
      roomData.weekday_rules?.forEach((rule: any) => {
        basePriceRules.push({
          id: rule.id,
          room_config_id: roomData.room_config_id,
          occupancy_type_id: rule.occupancy_type_id,
          meal_plan_id: rule.meal_plan_id,
          weekday_prices: rule.weekday_prices,
          currency_code: rule.currency_code || currentCurrency,
          default_values: rule.default_values,
          weekday_values: rule.weekday_values,
          weekday_margin_percentages: rule.weekday_margin_percentages,
          default_margin_percentage: rule.default_margin_percentage,
        });
      });

      // Extract seasonal rules from room pricing data
      roomData.seasonal_prices?.forEach((seasonalPrice: any) => {
        seasonalRules.push({
          id: seasonalPrice.id,
          base_price_rule_id: seasonalPrice.base_price_rule_id,
          start_date: seasonalPrice.start_date,
          end_date: seasonalPrice.end_date,
          amount: seasonalPrice.amount,
          currency_code: seasonalPrice.currency_code || currentCurrency,
          priority: seasonalPrice.priority || 100,
          name: seasonalPrice.name,
          description: seasonalPrice.description,
          metadata: seasonalPrice.metadata,
        });
      });
    });

    return { basePriceRules, seasonalRules };
  }, [comprehensiveData, currentCurrency]);

  // Handle missing pricing data
  if (!comprehensiveData) {
    return (
      <Container className="py-8">
        <div className="text-center py-12 bg-muted rounded-lg">
          <Tags className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <Text className="text-muted-foreground mb-4">
            No pricing data available for this hotel
          </Text>
          <Button
            variant="primary"
            size="small"
            onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Hotel
          </Button>
        </div>
      </Container>
    );
  }

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />
      <Container className="py-6">
        {/* Header with back button */}
        <div className="mb-6">
          <div className="flex items-center justify-between space-x-4 mb-4">
            <div>
              <Heading level="h1" className="text-2xl font-bold">
                {hotel.name} - Pricing Overview
              </Heading>
              <Text className="text-muted-foreground">
                View current pricing for all room types and seasonal periods
              </Text>
            </div>
            <Button
              variant="secondary"
              size="small"
              onClick={() => navigate(`/hotel-management/hotels/${slug}`)}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="w-4 h-4" />
              Back to Hotel
            </Button>
          </div>
        </div>

        {/* Calendar pricing view */}
        <PricingCalendarView
          hotelId={hotel.id}
          roomConfigs={comprehensiveData.roomConfigs || []}
          occupancyConfigs={comprehensiveData.occupancyConfigs || []}
          mealPlans={comprehensiveData.mealPlans || []}
          basePriceRules={transformedData.basePriceRules}
          seasonalRules={transformedData.seasonalRules}
          currencyCode={currentCurrency}
          onPriceUpdate={async () => {}} // No-op for read-only
          onRefetch={() => {}} // No-op for read-only
          canEdit={false} // Read-only mode
        />
      </Container>
    </>
  );
};



export default PageClient;
