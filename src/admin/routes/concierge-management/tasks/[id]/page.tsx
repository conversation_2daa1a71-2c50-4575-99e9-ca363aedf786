import React, { useState } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import {
  Container,
  Heading,
  Text,
  Button,
  Badge,
  StatusBadge,
  toast,
  usePrompt,
  IconButton,
} from "@camped-ai/ui";
import { SectionRow } from "../../../../../components/common/section/section-row";
import { ActionMenu } from "../../../../components/ActionMenu";
import { PencilSquare, Trash, ArrowLeft, Calendar, Tag } from "@camped-ai/icons";
import { Edit, User } from "lucide-react";
import { format } from "date-fns";
import {
  useConciergeTask,
  useDeleteConciergeTask,
  useCompleteConciergeTask,
} from "../../../../hooks/api/concierge-tasks";
import { TaskFormModal } from "../../../../components/concierge/task-form-modal";
import { UserDisplayName } from "../../../../components/concierge/user-display-name";
import { useRbac } from "../../../../hooks/use-rbac";

const TaskDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const prompt = usePrompt();
  const { hasPermission } = useRbac();

  const [isEditModalOpen, setIsEditModalOpen] = useState(false);

  // API hooks
  const { data: taskData, isLoading, error } = useConciergeTask(id!);
  const deleteTaskMutation = useDeleteConciergeTask();
  const completeTaskMutation = useCompleteConciergeTask();

  const task = taskData?.task;

  const handleDelete = async () => {
    if (!task) return;

    const confirmed = await prompt({
      title: "Delete Task",
      description: `Are you sure you want to delete the task "${task.title}"? This action cannot be undone.`,
      confirmText: "Delete",
      cancelText: "Cancel",
    });

    if (confirmed) {
      try {
        await deleteTaskMutation.mutateAsync(task.id);
        toast.success("Task deleted successfully");
        navigate("/concierge-management/tasks");
      } catch (error) {
        toast.error("Failed to delete task");
      }
    }
  };

  const handleComplete = async () => {
    if (!task) return;

    try {
      await completeTaskMutation.mutateAsync(task.id);
      toast.success("Task marked as completed");
    } catch (error) {
      toast.error("Failed to complete task");
    }
  };

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      pending: { label: "Pending", color: "grey" as const },
      in_progress: { label: "In Progress", color: "blue" as const },
      review: { label: "Review", color: "orange" as const },
      completed: { label: "Completed", color: "green" as const },
      cancelled: { label: "Cancelled", color: "red" as const },
    };

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.pending;
    return <StatusBadge color={config.color}>{config.label}</StatusBadge>;
  };

  const getPriorityBadge = (priority: string) => {
    const priorityConfig = {
      low: { label: "Low", color: "grey" as const },
      medium: { label: "Medium", color: "blue" as const },
      high: { label: "High", color: "orange" as const },
      urgent: { label: "Urgent", color: "red" as const },
    };

    const config = priorityConfig[priority as keyof typeof priorityConfig] || priorityConfig.medium;
    return <Badge color={config.color}>{config.label}</Badge>;
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "PPP");
    } catch (error) {
      return "Invalid date";
    }
  };

  if (isLoading) {
    return (
      <Container className="p-6">
        <Text>Loading task details...</Text>
      </Container>
    );
  }

  if (error || !task) {
    return (
      <Container className="p-6">
        <div className="text-center">
          <Heading level="h2">Task Not Found</Heading>
          <Text className="mt-2 text-ui-fg-subtle">
            The task you're looking for doesn't exist or has been deleted.
          </Text>
          <Button
            className="mt-4"
            onClick={() => navigate("/concierge-management/tasks")}
          >
            Back to Tasks
          </Button>
        </div>
      </Container>
    );
  }

  return (
    <>
      <Container className="divide-y p-0 mb-4">
        {/* Header */}
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <Heading level="h1" className="text-2xl">
              {task.title}
            </Heading>
            {getStatusBadge(task.status)}
            {getPriorityBadge(task.priority)}
          </div>

          <div className="flex items-center gap-2">
            {hasPermission("concierge_management:update") && (
              <div className="flex items-center gap-2">
                <IconButton size="small" onClick={() => setIsEditModalOpen(true)}>
                  <Edit className="h-4 w-4" />
                </IconButton>
                <Button variant="secondary" onClick={() => navigate("/concierge-management/tasks")} size="small">
                  <ArrowLeft className="h-4 w-4 mr-2" />
                  Back to Tasks
                </Button>
              </div>
            )}
          </div>
        </div>
      </Container>

      {/* Main Content */}
      <Container className="divide-y p-0">
        {/* Actions Header */}
        <div className="flex items-center justify-between px-6 py-4">
          <Heading level="h3">Task Details</Heading>
          <div className="flex items-center gap-x-2">
            {task.status !== "completed" && hasPermission("concierge_management:update") && (
              <Button
                variant="secondary"
                size="small"
                onClick={handleComplete}
                disabled={completeTaskMutation.isPending}
              >
                {completeTaskMutation.isPending ? "Completing..." : "Mark Complete"}
              </Button>
            )}
            {hasPermission("concierge_management:edit") && (
              <ActionMenu
                groups={[
                  {
                    actions: [
                      {
                        icon: <PencilSquare />,
                        label: "Edit Task",
                        onClick: () => setIsEditModalOpen(true),
                      },
                    ],
                  },
                  {
                    actions: [
                      {
                        icon: <Trash />,
                        label: "Delete Task",
                        onClick: handleDelete,
                      },
                    ],
                  },
                ]}
              />
            )}
          </div>
        </div>

        {/* Task Basic Information */}
        <SectionRow
          title="Task ID"
          value={task.id}
        />
        <SectionRow
          title="Title"
          value={task.title}
        />
        <SectionRow
          title="Description"
          value={
            task.description ? (
              <Text className="text-ui-fg-base">
                {task.description}
              </Text>
            ) : (
              <Text className="text-ui-fg-subtle">No description provided</Text>
            )
          }
        />

        {/* Status and Priority */}
        <SectionRow
          title="Status"
          value={getStatusBadge(task.status)}
        />
        <SectionRow
          title="Priority"
          value={getPriorityBadge(task.priority)}
        />

        {/* Assignment and Dates */}
        <SectionRow
          title="Assigned To"
          value={
            <div className="flex items-center gap-x-2">
              <User className="h-4 w-4 text-ui-fg-subtle" />
              <UserDisplayName userId={task.assigned_to} />
            </div>
          }
        />
        <SectionRow
          title="Due Date"
          value={
            <div className="flex items-center gap-x-2">
              <Calendar className="h-4 w-4 text-ui-fg-subtle" />
              <Text>
                {task.due_date ? formatDate(task.due_date) : "No due date"}
              </Text>
            </div>
          }
        />

        {/* Entity Information */}
        {task.entity_type && (
          <SectionRow
            title="Entity Type"
            value={
              <div className="flex items-center gap-x-2">
                <Tag className="h-4 w-4 text-ui-fg-subtle" />
                <Text className="capitalize">{task.entity_type}</Text>
              </div>
            }
          />
        )}
        {task.entity_id && (
          <SectionRow
            title="Entity ID"
            value={<Text>{task.entity_id}</Text>}
          />
        )}

        {/* Timestamps */}
        <SectionRow
          title="Created"
          value={
            <div>
              <Text>{formatDate(task.created_at)}</Text>
              {task.created_by && (
                <Text className="text-sm text-ui-fg-subtle">
                  by {task.created_by}
                </Text>
              )}
            </div>
          }
        />
        <SectionRow
          title="Last Updated"
          value={
            <div>
              <Text>{formatDate(task.updated_at)}</Text>
              {task.updated_by && (
                <Text className="text-sm text-ui-fg-subtle">
                  by {task.updated_by}
                </Text>
              )}
            </div>
          }
        />
      </Container>

      {/* Edit Modal */}
      <TaskFormModal
        isOpen={isEditModalOpen}
        onClose={() => setIsEditModalOpen(false)}
        task={task}
      />
    </>
  );
};

export default TaskDetailPage;
