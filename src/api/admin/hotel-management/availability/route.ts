import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { format, parseISO } from "date-fns";
import {
  consolidateAvailability,
  adjustForNoonToNoon,
} from "../../../../utils/consolidate-availability";
import { RoomInventoryStatus } from "../../../../modules/hotel-management/room-inventory/models/room-inventory";

/**
 * Maps internal room inventory status values to UI-friendly status values
 * @param status - The internal status value
 * @returns The UI-friendly status value
 */
function mapStatusForUI(status: string): string {
  switch (status) {
    case RoomInventoryStatus.AVAILABLE:
      return "available";
    case RoomInventoryStatus.BOOKED:
      return "booked";
    case RoomInventoryStatus.MAINTENANCE:
      return "maintenance";
    case RoomInventoryStatus.RESERVED:
      return "reserved";
    case RoomInventoryStatus.RESERVED_UNASSIGNED:
      return "reserved"; // Map reserved_unassigned to reserved for UI
    case RoomInventoryStatus.CART_RESERVED:
      return "reserved"; // Map cart_reserved to reserved for UI
    case RoomInventoryStatus.CLEANING:
      return "maintenance"; // Map cleaning to maintenance for UI
    case RoomInventoryStatus.ON_HOLD:
      return "on_hold"; // Map on_hold to on_hold for UI
    case RoomInventoryStatus.UNAVAILABLE:
      return "unavailable"; // Keep unavailable status for admin UI
    case RoomInventoryStatus.ON_DEMAND:
      return "on_demand"; // Keep on_demand status for admin UI
    default:
      return status;
  }
}

/**
 * Get availability for all rooms in a hotel
 *
 * @param hotel_id - The ID of the hotel to get availability for
 * @param start_date - The start date of the availability range (YYYY-MM-DD)
 * @param end_date - The end date of the availability range (YYYY-MM-DD)
 * @param consolidate - Whether to consolidate the availability data into date ranges (true/false)
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    // Resolve the product module service if available
    let productModuleService = null;

    try {
      productModuleService = req.scope.resolve(Modules.PRODUCT);
    } catch (error) {
      // Product module service not available
    }

    const { hotel_id, start_date, end_date, consolidate } = req.query;

    if (!start_date || !end_date) {
      return res.status(400).json({
        message: "Start date and end date are required",
      });
    }

    if (!hotel_id) {
      // Return empty data if hotel_id is not provided
      return res.json({
        room_configs: [],
        rooms: [],
        availability: [],
      });
    }

    // Get all room configurations for this hotel
    let roomConfigs = [];
    try {
      if (productModuleService) {
        // Try to use a filter that works with the current implementation
        const result: any = await productModuleService.listProducts(
          {
            is_giftcard: false,
          },
          {
            relations: ["variants"],
          }
        );

        // Filter products by hotel_id in metadata after fetching
        let products: any[] = [];

        if (Array.isArray(result)) {
          products = result;
        } else if (result && typeof result === "object") {
          // Try to extract products from different possible response formats
          if (Array.isArray(result.products)) {
            products = result.products;
          } else if (result.products) {
            products = [result.products];
          } else {
            // If we can't find products in the expected places, use the result itself
            products = [result];
          }
        }

        // Filter products by hotel_id in metadata
        roomConfigs = products.filter(
          (product: any) =>
            product &&
            product.metadata &&
            typeof product.metadata === "object" &&
            product.metadata.hotel_id === hotel_id
        );
      } else {
        roomConfigs = [];
      }
    } catch (error) {
      roomConfigs = [];
    }

    // If we couldn't get real room configurations, return empty data
    if (!roomConfigs.length) {
      return res.json({
        room_configs: [],
        rooms: [],
        availability: [],
      });
    }

    // Get all rooms (variants) for these room configurations
    let rooms = [];

    // Try to extract rooms from real data first
    for (const config of roomConfigs) {
      if (config.variants && Array.isArray(config.variants)) {
        // Add room configuration info to each room
        const roomsWithConfig = config.variants.map((variant: any) => {
          return {
            ...variant,
            room_config_id: config.id,
            config_name: config.title || config.name || "Unknown Configuration",
            room_number: variant.title || variant.name || `Room ${variant.id}`,
            name: variant.title || variant.name || `Room ${variant.id}`,
          };
        });

        rooms.push(...roomsWithConfig);
      }
    }

    // If we couldn't get real rooms, return empty data
    if (rooms.length === 0) {
      return res.json({
        room_configs: roomConfigs,
        rooms: [],
        availability: [],
      });
    }

    // Get availability data for each room
    const availability = [];
    // Create a separate array for unallocated bookings
    const bookings = [];
    // Convert query parameters to Date objects
    const startDateObj = new Date(start_date as string);
    const endDateObj = new Date(end_date as string);

    // We'll work directly with the date range from the database
    // No need to generate individual dates

    // Get the query service for direct database access
    const query = req.scope.resolve("query");

    // Process each room to get its availability
    for (const room of rooms) {
      try {
        // Try to get room inventory records from the database
        let roomInventories = [];
        try {
          const result = await query.graph({
            entity: "room_inventory",
            filters: {
              inventory_item_id: [room.id],
              from_date: { $gte: startDateObj },
              to_date: { $lte: endDateObj },
            },
            fields: [
              "id",
              "inventory_item_id",
              "from_date",
              "to_date",
              "available_quantity",
              "check_in_time",
              "check_out_time",
              "notes",
              "status",
              "order_id",
            ],
          });
          roomInventories = result.data || [];
        } catch (error) {
          roomInventories = [];
        }

        // Process room inventory records directly without expanding into individual dates
        for (const record of roomInventories) {
          const fromDate = new Date(record.from_date);
          const toDate = new Date(record.to_date);

          // Format dates as strings
          const fromDateStr = format(fromDate, "yyyy-MM-dd");
          const toDateStr = format(toDate, "yyyy-MM-dd");

          // Determine status and quantity
          const rawStatus =
            record.status ||
            (record.available_quantity > 0 ? "available" : "booked");
          const status = mapStatusForUI(rawStatus);
          const quantity = record.available_quantity || 0;

          // Check if we need to add booking details for "booked" status
          let bookingDetails = null;
          if (status === "booked" && record.order_id) {
            const booking = bookings.find(
              (order) => order.id === record.order_id
            );
            if (booking) {
              bookingDetails = {
                booking_id: booking.id,
                guest_name:
                  booking.metadata?.guest_name ||
                  (booking.billing_address?.first_name &&
                  booking.billing_address?.last_name
                    ? `${booking.billing_address.first_name} ${booking.billing_address.last_name}`
                    : "Guest"),
                guest_email:
                  booking.email || booking.metadata?.guest_email || "",
                guest_phone:
                  booking.metadata?.guest_phone ||
                  booking.billing_address?.phone ||
                  "",
                check_in_date: booking.metadata?.check_in_date || "",
                check_out_date: booking.metadata?.check_out_date || "",
                booking_status: booking.status || "",
                payment_status: booking.payment_status || "",
                total_amount: booking.total || 0,
                currency_code: booking.currency_code || "USD",
                special_requests: booking.metadata?.special_requests || "",
                booking_reference: booking.display_id || booking.id,
                created_at: booking.created_at || "",
                adults: booking.metadata?.adults || 1,
                children: booking.metadata?.children || 0,
                infants: booking.metadata?.infants || 0,
              };
            }
          }

          // Add the record directly to the availability array with from_date and to_date
          availability.push({
            room_id: room.id,
            room_number: room.room_number,
            room_name: room.name,
            config_name: room.config_name,
            from_date: fromDateStr,
            to_date: toDateStr,
            status,
            quantity,
            dynamic_price: null,
            notes: record.notes || "",
            order_id: record.order_id || "",
            booking_details: bookingDetails, // Add booking details when status is "booked"
          });
        }

        // If no inventory records were found for this room, add a default "unavailable" entry
        // for the entire date range
        if (roomInventories.length === 0) {
          availability.push({
            room_id: room.id,
            room_number: room.room_number,
            room_name: room.name,
            config_name: room.config_name,
            from_date: format(startDateObj, "yyyy-MM-dd"),
            to_date: format(endDateObj, "yyyy-MM-dd"),
            status: "unavailable",
            quantity: 0,
            dynamic_price: null,
            notes: "",
            order_id: "",
          });
        }
      } catch (error) {
        // Add default availability (all available) for this room
        availability.push({
          room_id: room.id,
          room_number: room.room_number,
          room_name: room.name,
          config_name: room.config_name,
          from_date: format(startDateObj, "yyyy-MM-dd"),
          to_date: format(endDateObj, "yyyy-MM-dd"),
          status: "available",
          quantity: 1,
          dynamic_price: null,
          notes: "",
          order_id: "",
        });
      }
    }

    // Fetch unallocated bookings (orders without reservations in metadata for the given period)

    try {
      // Get all orders for this hotel
      // Using any type to bypass TypeScript strict checking for metadata filters
      const { data: hotelOrders } = await query.graph({
        entity: "order",
        filters: {
          // Only include orders that are not cancelled or completed
          status: { $in: ["pending"] },
        } as any, // Cast to any to bypass TypeScript checking
        fields: ["id", "metadata", "created_at", "status"],
      });

      // Process each order to check if it has reservations in metadata
      if (hotelOrders && hotelOrders.length > 0) {
        for (const order of hotelOrders) {
          try {
            // Skip orders that don't belong to this hotel
            if (order.metadata?.hotel_id !== hotel_id) {
              continue;
            }

            // Check if order has reservations in metadata
            const hasReservations =
              order.metadata?.reservations &&
              Array.isArray(order.metadata.reservations) &&
              order.metadata.reservations.length > 0;

            // If no reservations, this is an unallocated booking
            if (!hasReservations) {
              // Get check-in and check-out dates from metadata
              const checkInDate = order.metadata?.check_in_date
                ? new Date(order.metadata.check_in_date as string)
                : null;
              const checkOutDate = order.metadata?.check_out_date
                ? new Date(order.metadata.check_out_date as string)
                : null;

              // Skip orders that don't overlap with our date range
              if (!checkInDate || !checkOutDate) {
                continue;
              }

              // Check if the order's date range overlaps with our view date range
              const orderStartsBeforeViewEnds = checkInDate <= endDateObj;
              const orderEndsAfterViewStarts = checkOutDate >= startDateObj;

              // Skip if the order's date range doesn't overlap with our view date range
              if (!(orderStartsBeforeViewEnds && orderEndsAfterViewStarts)) {
                continue;
              }

              // Add to bookings array instead of availability
              bookings.push(order);
            }
          } catch (error) {
            // Error processing order
          }
        }
      }
    } catch (error) {
      // Error fetching unallocated bookings from orders
    }

    // Check if the client wants consolidated availability data
    const shouldConsolidate = consolidate === "true" || consolidate === "1";

    if (shouldConsolidate) {
      // First, we need to convert the date ranges to individual dates
      // This is necessary because the consolidateAvailability function expects individual dates
      const expandedAvailability = [];

      for (const record of availability) {
        const fromDate = parseISO(record.from_date);
        const toDate = parseISO(record.to_date);

        // Generate dates from from_date to to_date (exclusive)
        let currentDate = fromDate;
        while (currentDate < toDate) {
          expandedAvailability.push({
            room_id: record.room_id,
            room_number: record.room_number,
            room_name: record.room_name,
            config_name: record.config_name,
            date: format(currentDate, "yyyy-MM-dd"),
            status: mapStatusForUI(record.status),
            quantity: record.quantity,
            dynamic_price: record.dynamic_price,
            notes: record.notes,
            order_id: record.order_id,
          });

          // Move to the next day
          currentDate = new Date(currentDate);
          currentDate.setDate(currentDate.getDate() + 1);
        }
      }

      // Now consolidate the expanded availability
      const consolidatedAvailability =
        consolidateAvailability(expandedAvailability);

      // Apply noon-to-noon concept
      const adjustedAvailability = adjustForNoonToNoon(
        consolidatedAvailability
      );

      // Return the consolidated and adjusted availability data
      return res.json({
        room_configs: roomConfigs,
        rooms,
        availability: adjustedAvailability,
        bookings: bookings,
      });
    } else {
      // Return the original availability data
      return res.json({
        room_configs: roomConfigs,
        rooms,
        availability,
        bookings: bookings,
      });
    }
  } catch (error) {
    return res.status(500).json({
      message: "Error getting hotel availability",
      error: error.message,
    });
  }
};
