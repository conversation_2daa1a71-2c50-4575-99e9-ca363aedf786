import React from "react";
import { useDroppable } from "@dnd-kit/core";
import { SortableContext, verticalListSortingStrategy } from "@dnd-kit/sortable";
import { Heading, Text, Badge } from "@camped-ai/ui";
import {
  Clock,
  Pause,
  AlertTriangle,
  CheckCircle,
  XCircle
} from "lucide-react";
import { TaskScreenData } from "../../routes/concierge-management/tasks/loader";
import KanbanTaskCard from "./kanban-task-card";

// Status configuration
const statusConfig = {
  pending: {
    title: "Pending",
    icon: Clock,
    color: "orange",
    bgColor: "bg-orange-50",
    borderColor: "border-orange-200",
  },
  in_progress: {
    title: "In Progress",
    icon: Pause,
    color: "blue",
    bgColor: "bg-blue-50",
    borderColor: "border-blue-200",
  },
  review: {
    title: "Review",
    icon: AlertTriangle,
    color: "purple",
    bgColor: "bg-purple-50",
    borderColor: "border-purple-200",
  },
  completed: {
    title: "Completed",
    icon: CheckCircle,
    color: "green",
    bgColor: "bg-green-50",
    borderColor: "border-green-200",
  },
  cancelled: {
    title: "Cancelled",
    icon: XCircle,
    color: "red",
    bgColor: "bg-red-50",
    borderColor: "border-red-200",
  },
} as const;

interface KanbanColumnProps {
  status: keyof typeof statusConfig;
  tasks: TaskScreenData[];
  onEdit?: (task: TaskScreenData) => void;
  onDelete?: (task: TaskScreenData) => void;
  onCopyId?: (task: TaskScreenData) => void;
  hasEditPermission?: boolean;
  hasDeletePermission?: boolean;
  updatingTaskId?: string | null;
}

export const KanbanColumn: React.FC<KanbanColumnProps> = ({
  status,
  tasks,
  onEdit,
  onDelete,
  onCopyId,
  hasEditPermission = false,
  hasDeletePermission = false,
  updatingTaskId = null,
}) => {
  const config = statusConfig[status];
  const IconComponent = config.icon;

  const { setNodeRef, isOver } = useDroppable({
    id: status,
    data: {
      type: "column",
      status,
    },
  });

  const taskIds = tasks.map(task => task.id);

  return (
    <div className="flex flex-col h-full min-w-[280px] max-w-[320px]">
      {/* Column Header */}
      <div className={`
        flex items-center justify-between p-4 rounded-t-lg border-t border-l border-r
        ${config.bgColor} ${config.borderColor}
      `}>
        <div className="flex items-center gap-2">
          <IconComponent className="h-4 w-4" />
          <Heading level="h3" className="text-sm font-medium">
            {config.title}
          </Heading>
        </div>
        <Badge color={config.color} size="small">
          {tasks.length}
        </Badge>
      </div>

      {/* Column Content */}
      <div
        ref={setNodeRef}
        className={`
          flex-1 p-3 border-l border-r border-b rounded-b-lg min-h-[400px]
          ${config.bgColor} ${config.borderColor}
          ${isOver ? 'bg-opacity-80 ring-2 ring-blue-300' : ''}
          transition-all duration-200
        `}
      >
        <SortableContext items={taskIds} strategy={verticalListSortingStrategy}>
          <div className="space-y-3">
            {tasks.length === 0 ? (
              <div className="flex flex-col items-center justify-center py-8 text-center">
                <IconComponent className="h-8 w-8 text-ui-fg-muted mb-2" />
                <Text className="text-sm text-ui-fg-muted">
                  No {config.title.toLowerCase()} tasks
                </Text>
              </div>
            ) : (
              tasks.map((task) => (
                <KanbanTaskCard
                  key={task.id}
                  task={task}
                  onEdit={onEdit}
                  onDelete={onDelete}
                  onCopyId={onCopyId}
                  hasEditPermission={hasEditPermission}
                  hasDeletePermission={hasDeletePermission}
                  isUpdating={updatingTaskId === task.id}
                />
              ))
            )}
          </div>
        </SortableContext>
      </div>
    </div>
  );
};

export default KanbanColumn;
