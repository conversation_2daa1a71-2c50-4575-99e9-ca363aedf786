import React, { useState, useEffect } from "react";
import { useSearchParams } from "react-router-dom";
import {
  Button,
  DropdownMenu,
  Input,
  Badge,
  Text,
  Container,
} from "@camped-ai/ui";
import {
  Filter,
  Search,
  X,
  ChevronDown,
  Calendar,
  User,
  Building,
  CheckCircle,
  MapPin,
} from "lucide-react";
import { UserSelector } from "./user-selector";
import { UserMultiSelect } from "./user-multi-select";
import { HotelMultiSelect } from "./hotel-multi-select";
import DateRangePicker from "../shared/date-range-picker";
import { MultiSelect } from "../common/MultiSelect";
import { ViewToggle, ViewMode } from "./view-toggle";

export interface BookingFilterState {
  search: string;
  hotel_id: string[];
  assigned_to: string[];
  status: string[];
  check_in_date_gte?: string;
  check_in_date_lte?: string;
  check_out_date_gte?: string;
  check_out_date_lte?: string;
  customer_name?: string;
}

interface BookingFilterControlsProps {
  onFiltersChange: (filters: BookingFilterState) => void;
  viewMode: ViewMode;
  onViewModeChange: (mode: ViewMode) => void;
  className?: string;
}

// Status options for booking filters
const statusOptions = [
  { value: "not_started", label: "Not Started" },
  { value: "in_progress", label: "In Progress" },
  { value: "waiting_customer", label: "Waiting Customer" },
  { value: "ready_to_finalize", label: "Ready to Finalize" },
  { value: "completed", label: "Completed" },
  { value: "cancelled", label: "Cancelled" },
  { value: "on_hold", label: "On Hold" },
];

export const BookingFilterControls: React.FC<BookingFilterControlsProps> = ({
  onFiltersChange,
  viewMode,
  onViewModeChange,
  className = "",
}) => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [searchValue, setSearchValue] = useState("");
  const [customerNameValue, setCustomerNameValue] = useState("");
  const [showFilters, setShowFilters] = useState(false);
  const [filters, setFilters] = useState<BookingFilterState>({
    search: "",
    hotel_id: [],
    assigned_to: [],
    status: [],
  });

  // Initialize filters from URL params
  useEffect(() => {
    const initialFilters: BookingFilterState = {
      search: searchParams.get("q") || "",
      hotel_id: searchParams.get("hotel_id")?.split(",").filter(Boolean) || [],
      assigned_to: searchParams.get("assigned_to")?.split(",").filter(Boolean) || [],
      status: searchParams.get("status")?.split(",").filter(Boolean) || [],
      check_in_date_gte: searchParams.get("check_in_date_gte") || undefined,
      check_in_date_lte: searchParams.get("check_in_date_lte") || undefined,
      check_out_date_gte: searchParams.get("check_out_date_gte") || undefined,
      check_out_date_lte: searchParams.get("check_out_date_lte") || undefined,
      customer_name: searchParams.get("customer_name") || undefined,
    };

    setFilters(initialFilters);
    setSearchValue(initialFilters.search);
    setCustomerNameValue(initialFilters.customer_name || "");
  }, [searchParams]);

  const updateFilters = (updatedFilters: Partial<BookingFilterState>) => {
    const newFilters = { ...filters, ...updatedFilters };
    setFilters(newFilters);

    // Update URL params
    const newSearchParams = new URLSearchParams(searchParams);

    // Handle search
    if (newFilters.search) {
      newSearchParams.set("q", newFilters.search);
    } else {
      newSearchParams.delete("q");
    }

    // Handle array filters
    ["hotel_id", "assigned_to", "status"].forEach((key) => {
      const values = newFilters[key as keyof BookingFilterState] as string[];
      if (values && values.length > 0) {
        newSearchParams.set(key, values.join(","));
      } else {
        newSearchParams.delete(key);
      }
    });

    // Handle date filters
    ["check_in_date_gte", "check_in_date_lte", "check_out_date_gte", "check_out_date_lte", "customer_name"].forEach((key) => {
      const value = newFilters[key as keyof BookingFilterState] as string;
      if (value) {
        newSearchParams.set(key, value);
      } else {
        newSearchParams.delete(key);
      }
    });

    // Reset to page 1 when filters change
    newSearchParams.delete("page");

    setSearchParams(newSearchParams);
    onFiltersChange(newFilters);
  };

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    updateFilters({ search: value });
  };

  const handleCustomerNameChange = (value: string) => {
    setCustomerNameValue(value);
    updateFilters({ customer_name: value });
  };

  const toggleArrayFilter = (key: keyof BookingFilterState, value: string) => {
    const currentArray = filters[key] as string[];
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value];

    updateFilters({ [key]: newArray });
  };

  const handleDateRangeChange = (type: "check_in" | "check_out", range: { from: Date; to: Date }) => {
    const updates: Partial<BookingFilterState> = {};

    if (type === "check_in") {
      // Convert to ISO datetime strings for API compatibility
      const fromDate = new Date(range.from);
      fromDate.setHours(0, 0, 0, 0); // Start of day
      const toDate = new Date(range.to);
      toDate.setHours(23, 59, 59, 999); // End of day

      updates.check_in_date_gte = fromDate.toISOString();
      updates.check_in_date_lte = toDate.toISOString();
    } else {
      // Convert to ISO datetime strings for API compatibility
      const fromDate = new Date(range.from);
      fromDate.setHours(0, 0, 0, 0); // Start of day
      const toDate = new Date(range.to);
      toDate.setHours(23, 59, 59, 999); // End of day

      updates.check_out_date_gte = fromDate.toISOString();
      updates.check_out_date_lte = toDate.toISOString();
    }

    updateFilters(updates);
  };

  const clearAllFilters = () => {
    const clearedFilters: BookingFilterState = {
      search: "",
      hotel_id: [],
      assigned_to: [],
      status: [],
    };

    setFilters(clearedFilters);
    setSearchValue("");
    setCustomerNameValue("");

    // Clear URL params
    const newSearchParams = new URLSearchParams();
    setSearchParams(newSearchParams);
    onFiltersChange(clearedFilters);
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (filters.search) count++;
    if (filters.hotel_id.length > 0) count++;
    if (filters.assigned_to.length > 0) count++;
    if (filters.status.length > 0) count++;
    if (filters.check_in_date_gte || filters.check_in_date_lte) count++;
    if (filters.check_out_date_gte || filters.check_out_date_lte) count++;
    if (filters.customer_name) count++;
    return count;
  };

  const activeFilterCount = getActiveFilterCount();

  // Auto-show filters if there are active filters (for persistence across view switches)
  useEffect(() => {
    if (activeFilterCount > 0 && !showFilters) {
      setShowFilters(true);
    }
  }, [activeFilterCount, showFilters]);

  return (
    <Container className={`border-b border-gray-200 ${className}`}>
      {/* Always visible search bar, view toggle, and filter controls */}
      <div className="p-4 bg-white">
        <div className="flex flex-col lg:flex-row items-start lg:items-center gap-3">
          {/* Left side: Search inputs */}
          <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3 flex-1">
            {/* General Search Input */}
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-ui-fg-muted" />
              <Input
                placeholder="Search bookings..."
                value={searchValue}
                onChange={(e) => handleSearchChange(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Customer Name Search */}
            <div className="relative flex-1 max-w-md">
              <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-ui-fg-muted" />
              <Input
                placeholder="Search by customer name..."
                value={customerNameValue}
                onChange={(e) => handleCustomerNameChange(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>

          {/* Right side: Controls */}
          <div className="flex items-center gap-3 flex-shrink-0">
            {/* Filter Toggle Button */}
            <Button
              variant="secondary"
              size="small"
              onClick={() => setShowFilters(!showFilters)}
              className="whitespace-nowrap"
            >
              <Filter className="h-4 w-4 mr-2" />
              Filters
              {activeFilterCount > 0 && (
                <Badge size="xsmall" className="ml-2">
                  {activeFilterCount}
                </Badge>
              )}
            </Button>

            {/* Clear All Filters */}
            {activeFilterCount > 0 && (
              <Button
                variant="secondary"
                size="small"
                onClick={clearAllFilters}
                className="whitespace-nowrap"
              >
                <X className="h-4 w-4 mr-2" />
                Clear All
              </Button>
            )}

            {/* View Toggle */}
            <ViewToggle
              viewMode={viewMode}
              onViewModeChange={onViewModeChange}
            />
          </div>
        </div>
      </div>

      {/* Collapsible Filter Panel */}
      {showFilters && (
        <div className="p-4 bg-gray-50 border-t border-gray-200">
          <div className="flex flex-wrap items-center gap-3">
            {/* Hotel Filter */}
            <div className="min-w-[200px]">
              <Text className="text-sm font-medium mb-2">Hotel</Text>
              <HotelMultiSelect
                selectedHotelIds={filters.hotel_id}
                onHotelsChange={(values) => updateFilters({ hotel_id: values })}
                placeholder="Select hotels..."
                showActiveOnly={true}
              />
            </div>

            {/* Assignee Filter */}
            <div className="min-w-[200px]">
              <Text className="text-sm font-medium mb-2">Assigned To</Text>
              <UserMultiSelect
                selectedUserIds={filters.assigned_to}
                onUsersChange={(values) => updateFilters({ assigned_to: values })}
                placeholder="Select assignees..."
                includeUnassigned={true}
              />
            </div>

            {/* Status Filter */}
            <DropdownMenu>
              <DropdownMenu.Trigger asChild>
                <Button variant="secondary" size="small">
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Status
                  {filters.status.length > 0 && (
                    <Badge size="xsmall" className="ml-2">
                      {filters.status.length}
                    </Badge>
                  )}
                  <ChevronDown className="h-4 w-4 ml-2" />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content align="start" className="w-56">
                {statusOptions.map((option) => (
                  <DropdownMenu.Item
                    key={option.value}
                    onClick={() => toggleArrayFilter("status", option.value)}
                    className="flex items-center gap-2"
                  >
                    <input
                      type="checkbox"
                      checked={filters.status.includes(option.value)}
                      onChange={() => {}}
                      className="rounded border-input text-primary focus:ring-primary"
                    />
                    {option.label}
                  </DropdownMenu.Item>
                ))}
              </DropdownMenu.Content>
            </DropdownMenu>

            {/* Check-in Date Range */}
            <div className="min-w-[200px]">
              <Text className="text-sm font-medium mb-2">Check-in Date</Text>
              <DateRangePicker
                initialDateFrom={filters.check_in_date_gte ? new Date(filters.check_in_date_gte) : undefined}
                initialDateTo={filters.check_in_date_lte ? new Date(filters.check_in_date_lte) : undefined}
                onRangeChange={(range) => handleDateRangeChange("check_in", range)}
              />
            </div>

            {/* Check-out Date Range */}
            <div className="min-w-[200px]">
              <Text className="text-sm font-medium mb-2">Check-out Date</Text>
              <DateRangePicker
                initialDateFrom={filters.check_out_date_gte ? new Date(filters.check_out_date_gte) : undefined}
                initialDateTo={filters.check_out_date_lte ? new Date(filters.check_out_date_lte) : undefined}
                onRangeChange={(range) => handleDateRangeChange("check_out", range)}
              />
            </div>
          </div>
        </div>
      )}
    </Container>
  );
};
